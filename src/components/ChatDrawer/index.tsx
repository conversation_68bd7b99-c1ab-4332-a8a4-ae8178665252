import { But<PERSON>, <PERSON>er, Modal } from "antd";

interface ChatDialogProps {
    open: boolean;
    title?: string;
    onOk?: () => void;
    onCancel?: () => void;
    children: React.ReactNode;
    okText?: string;
    cancelText?: string;
    confirmLoading: boolean;
    width?: string | number;
    className?: string;
    showFooter?: boolean;
    footer?: React.ReactNode;
}

export default function ChatDialog(props: ChatDialogProps) {
    const showFooter = props.showFooter ?? true;

    const handleClose = () => {
        if (props.confirmLoading) return;
        props.onCancel?.();
    };

    const DefaultFooter = () => {
        return (
            <div className="flex-row justify-end items-center gap-8">
                <Button loading={props.confirmLoading} onClick={handleClose}>
                    {props.cancelText ?? "取消"}
                </Button>
                <Button type="primary" loading={props.confirmLoading} onClick={props.onOk}>
                    {props.okText ?? "确认"}
                </Button>
            </div>
        );
    };

    // TODO： 不配置destroyOnHidden时同样接口不走

    return (
        <Drawer
            open={props.open}
            destroyOnHidden
            title={props.title ?? "标题"}
            onClose={handleClose}
            keyboard={false}
            maskClosable={false}
            getContainer={document.fullscreenElement ? false : document.body}
            footer={showFooter && (props?.footer ?? <DefaultFooter />)}
            width={props?.width ?? 800}
            classNames={{
                body: props?.className ?? "chat-drawer-body",
                header: "chat-drawer-header",
                footer: "chat-drawer-footer",
            }}
        >
            {props.children}
        </Drawer>
    );
}
