"use client";

import React from "react";
import { Tooltip } from "antd";
import styles from "./index.module.scss";

export interface TextViewProps {
    /** 要显示的文本内容 */
    text: string | (() => React.ReactNode);
    /** 最大宽度，超出此宽度将显示省略号，默认为100% */
    maxWidth?: number | string;
    /** 文本截断行数，默认为1 */
    lines?: number;
    /** tooltip的位置 */
    placement?:
        | "top"
        | "left"
        | "right"
        | "bottom"
        | "topLeft"
        | "topRight"
        | "bottomLeft"
        | "bottomRight"
        | "leftTop"
        | "leftBottom"
        | "rightTop"
        | "rightBottom";
    /** 是否显示tooltip，当text溢出时默认为true */
    showTooltip?: boolean;
    /** 自定义tooltip显示内容，默认为text */
    tooltipContent?: React.ReactNode;
    /** 自定义类名 */
    className?: string;
    /** 自定义样式 */
    style?: React.CSSProperties;
    /** tooltip背景色，默认为白色 */
    tooltipColor?: string;
    /** tooltip内容字体大小，默认为14px */
    fontSize?: string;
}

export function TextView({
    text,
    maxWidth = "100%",
    lines = 1,
    placement = "top",
    showTooltip = true,
    tooltipContent,
    className,
    style,
    tooltipColor = "#fff",
    fontSize = "14px",
}: TextViewProps) {
    const [isEllipsis, setIsEllipsis] = React.useState(false);
    const textRef = React.useRef<HTMLDivElement>(null);

    React.useEffect(() => {
        if (textRef.current) {
            const element = textRef.current;
            // 检查是否文本溢出
            const isOverflow = element.scrollWidth > element.clientWidth || element.scrollHeight > element.clientHeight;

            setIsEllipsis(isOverflow);
        }
    }, [text, maxWidth, lines]);

    const textStyle: React.CSSProperties = {
        width: "100%",
        maxWidth: typeof maxWidth === "number" ? `${maxWidth}px` : maxWidth,
        overflow: "hidden",
        textOverflow: "ellipsis",
        whiteSpace: lines === 1 ? "nowrap" : "normal",
        ...(lines > 1
            ? {
                  display: "-webkit-box",
                  WebkitLineClamp: lines,
                  WebkitBoxOrient: "vertical",
              }
            : {}),
        ...style,
    };

    const content = (
        <div ref={textRef} className={`${styles.textView} ${className || ""}`} style={textStyle}>
            {typeof text === "string" ? text : text()}
        </div>
    );

    if (!showTooltip || !isEllipsis) {
        return content;
    }

    return (
        <Tooltip
            title={tooltipContent || (typeof text === "string" ? text : text())}
            placement={placement}
            color={tooltipColor}
            getPopupContainer={() => document.body}
            styles={{
                body: {
                    color: "rgba(0, 0, 0, 0.88)",
                    fontSize,
                },
            }}
        >
            {content}
        </Tooltip>
    );
}
