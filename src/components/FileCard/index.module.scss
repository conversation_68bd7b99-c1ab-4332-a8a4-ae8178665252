.card-wrapper {
    width: 200px;
    background-color: #f6f6f6;
    padding: 8px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    cursor: pointer;
    min-width: 200px;
    
    &:hover {
        background-color: #f2f2f2;
    }
    .card-icon {
        flex-shrink: 0;
        width: 48px;
        height: 48px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        background-color: #e0e0e0;
        border-radius: 8px;

        .file-preview-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }

        .file-type-placeholder {
            font-size: 12px;
            font-weight: bold;
            color: #555;
            padding: 4px;
            text-align: center;
        }
    }
    .card-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-width: 0;
        .card-name {
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            color: rgba(0, 0, 0, 0.85);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .card-size {
            font-size: 11px;
            margin-top: 2px;
            color: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            gap: 4px;
        }
    }
}
