import { useCallback, useEffect, useLayoutEffect, useRef, useState } from "react";
import styles from "./index.module.scss";
import { Tooltip } from "antd";
import useFullScreenStore from "@/app/store/modules/fullscreen";
import { debounce } from "lodash-es";

interface ITags {
    name: string;
    isMatched: boolean;
}

/**
 *
 * @param params tags: 标签列表; wrapperWidth: 外层容器宽度，用于监听容器宽度变化，重新计算是否展示悬停; color?: 标签颜色, 默认为灰色
 * @returns
 */
const Tags = ({ tags, color }: { tags: ITags[]; color?: string }) => {
    const fullScreenContainerId = useFullScreenStore((state) => state.fullScreenContainerId);
    const [tagsList, setTagsList] = useState<ITags[]>([]);
    const [overflowTagsList, setOverflowTagsList] = useState<ITags[]>([]);
    const [isOverflowing, setIsOverflowing] = useState<boolean>(false);
    const [parentWidth, setParentWidth] = useState<number>(200);

    const wrapperRef = useRef<HTMLDivElement>(null);
    const contentRef = useRef<HTMLDivElement>(null);
    const previousWidthRef = useRef(null);
    const calculatingRef = useRef(false); // 标记是否正在计算中

    // 防抖函数，减少宽度更新频率
    const debouncedSetWidth = useRef(
        debounce((width) => {
            if (previousWidthRef.current !== width) {
                previousWidthRef.current = width;
                setParentWidth(width);
            }
        }, 200)
    ).current;

    useEffect(() => {
        // 监听父元素宽度，当父元素宽度变化时，重新计算是否展示悬停
        const observe = new ResizeObserver((entries) => {
            for (const entry of entries) {
                const { width } = entry.contentRect;
                debouncedSetWidth(Math.floor(width));
            }
        });

        if (wrapperRef.current?.parentElement) {
            const dom = wrapperRef.current?.parentElement;
            observe.observe(dom);
        }

        return () => observe.disconnect();
    }, []);

    useEffect(() => {
        if (!parentWidth || !contentRef.current || calculatingRef.current) return;

        // 标记正在计算
        calculatingRef.current = true;
        // setTagsList(copy); // 初始化tagsList

        // 创建一个离屏DOM元素来测量
        const offscreenDiv = document.createElement("div");
        offscreenDiv.style.position = "absolute";
        offscreenDiv.style.visibility = "hidden";
        offscreenDiv.style.width = `${parentWidth}px`;
        // 复制原始容器的样式以确保测量准确
        if (contentRef.current) {
            const styles = window.getComputedStyle(contentRef.current);
            for (const style of Object.values(styles)) {
                if (styles.getPropertyValue(style)) {
                    offscreenDiv.style[style as any] = styles.getPropertyValue(style);
                }
            }
        }
        // 为每个标签创建DOM元素
        const copy = JSON.parse(JSON.stringify(tags));
        copy.forEach((tag: ITags) => {
            const tagElement = document.createElement("div");
            tagElement.textContent = tag.name;
            tagElement.className = "tags-item"; // 使用与实际标签相同的类

            // 应用标签的样式
            tagElement.style.padding = "4px 8px";
            tagElement.style.width = "auto";
            tagElement.style.whiteSpace = "nowrap";

            offscreenDiv.appendChild(tagElement);
        });
        // 添加到文档以进行测量
        document.body.appendChild(offscreenDiv);

        // 测量逻辑
        const overflow = offscreenDiv.scrollWidth > parentWidth;
        if (!overflow) {
            // 如果没有溢出，显示所有标签
            document.body.removeChild(offscreenDiv);
            setIsOverflowing(false);
            setTagsList(copy);
            setOverflowTagsList([]);
            calculatingRef.current = false;
            return;
        }

        // 计算可见标签
        let itemWidth = 0;
        let len = 0;
        const plusTagWidth = 70;

        for (const item of offscreenDiv.children) {
            const itemRect = item.getBoundingClientRect();
            itemWidth += itemRect.width;
            if (itemWidth + plusTagWidth >= parentWidth) {
                break;
            }
            len++;
        }

        // 从DOM中移除离屏元素
        document.body.removeChild(offscreenDiv);
        const resultLen = Math.max(0, len); // 确保不会是负数
        setIsOverflowing(true);
        setTagsList(copy.slice(0, resultLen));
        setOverflowTagsList(copy.slice(resultLen));

        // 标记计算完成
        calculatingRef.current = false;
    }, [parentWidth, tags]);

    const overflowTags = () => {
        return (
            <div style={{ display: "inline-block", background: "#fff" }}>
                {overflowTagsList.map((tag, index) => (
                    <div
                        key={index}
                        className={`${styles["tags-item"]} ${!tag.isMatched && styles["disabled"]}`}
                        style={{
                            marginBottom: "4px",
                            width: "auto",
                            color: color ?? "rgba(0, 0, 0, 0.86)",
                            backgroundColor: color ? `${color}25` : "rgba(0, 0, 0, 0.04)",
                        }}
                    >
                        {tag.name}
                    </div>
                ))}
            </div>
        );
    };

    return (
        <div ref={wrapperRef} className={styles["tags-wrapper"]}>
            <div ref={contentRef} className={styles["tags-content"]}>
                {tagsList.map((tag, index) => (
                    <div
                        key={index}
                        className={`${styles["tags-item"]} ${!tag.isMatched && styles["disabled"]}`}
                        style={{
                            color: color ?? "rgba(0, 0, 0, 0.86)",
                            backgroundColor: color ? `${color}25` : "rgba(0, 0, 0, 0.04)",
                        }}
                    >
                        {tag.name}
                    </div>
                ))}
                {isOverflowing && (
                    <Tooltip
                        color="#fff"
                        title={overflowTags}
                        styles={{
                            root: {
                                maxWidth: "none",
                            },
                            body: {
                                maxHeight: "400px",
                                overflow: "auto",
                            },
                        }}
                        getPopupContainer={() => document.getElementById(fullScreenContainerId) ?? document.body}
                    >
                        <span className={`${styles["tags-item"]} ${styles["disabled"]}`}>
                            +{overflowTagsList.length}
                        </span>
                    </Tooltip>
                )}
            </div>
        </div>
    );
};

export default Tags;
