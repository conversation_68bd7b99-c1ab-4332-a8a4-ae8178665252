"use client";

import React, { useState, useEffect } from "react";
import { Watermark } from "antd";
import useUserInfoStore from "@/app/store/userInfo";

const formatDateTime = (date: Date): string => {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${year}年${month}月${day}日 ${hours}:${minutes}`;
};

/**
 * 纯客户端水印组件
 * 完全避开 SSR，只在客户端渲染
 */
function ClientOnlyWatermarkContent({ children }: { children: React.ReactNode }) {
    const user = useUserInfoStore((state) => state.user);
    const [currentTime, setCurrentTime] = useState(() => formatDateTime(new Date()));
    const [mounted, setMounted] = useState(false);

    const env = process.env.NEXT_PUBLIC_NODE_ENV;

    useEffect(() => {
        setMounted(true);
        const intervalId = setInterval(() => {
            setCurrentTime(formatDateTime(new Date()));
        }, 60000); // 每分钟更新一次时间
        return () => clearInterval(intervalId);
    }, []);

    // 在客户端挂载之前，不渲染水印
    if (!mounted) {
        return <>{children}</>;
    }

    let firstLine = user?.emp_name && user?.emp_id ? `${user.emp_name} ${user.emp_id}` : "汇川招聘智能助手";
    if (env === "test") {
        firstLine += "(test)";
    }

    const watermarkContent = [firstLine, currentTime];

    return (
        <Watermark
            content={watermarkContent}
            style={{ height: "100%", width: "100%" }}
            gap={[360, 180]}
            font={{ color: "rgba(0, 0, 0, 0.08)" }}
        >
            {children}
        </Watermark>
    );
}

export function ClientOnlyWatermark({ children }: { children: React.ReactNode }) {
    const [isClient, setIsClient] = useState(false);

    useEffect(() => {
        setIsClient(true);
    }, []);

    // 在服务端或客户端第一次渲染时，不显示水印
    if (!isClient) {
        return <>{children}</>;
    }

    return <ClientOnlyWatermarkContent>{children}</ClientOnlyWatermarkContent>;
}
