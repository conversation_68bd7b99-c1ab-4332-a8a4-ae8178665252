.profile-fit-wrapper {
    width: 580px;
    max-height: 450px;
    overflow-x: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    :global {
        .ant-rate {
            font-size: 16px;
        }
    }

    .profile-fit__label {
        font-weight: 600;
        font-size: 14px;
        color: var(--main-text-color);
        margin-bottom: 16px;
    }

    .profile-fit__top {
        display: flex;
        align-items: center;
        border-radius: 12px;
        background: linear-gradient(90.61deg, #0099f21a -2.07%, #843dff1a 67.42%, #33d1cc1a 111.94%);
        padding: 12px;
        gap: 12px;

        .profile-fit__top-score-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            color: var(--sub-text-color);
            gap: 8px;
            padding: 12px;
            border-radius: 8px;
            background: #ffffffcc;
            backdrop-filter: blur(8px);

            .profile-fit__top-score {
                color: var(--primary);
                font-size: 24px;
                line-height: 34px;
                margin-right: 4px;
                font-family: Kingsoft_Cloud_Font;
            }
        }

        .profile-fit__top-item {
            flex: 1;
            .profile-fit__top-item-label {
                color: var(--sub-text-color);
            }

            .profile-fit__top-item-value {
                margin-top: 4px;
                color: var(--main-text-color);
                font-size: 20px;
                line-height: 28px;
                font-family: Kingsoft_Cloud_Font;
            }

            .profile-fit__top-item-unit {
                font-size: 14px;
                color: var(--sub-text-color);
                margin-left: 2px;
            }
        }
    }

    .profile-fit__middle {
        margin: 16px 0;
        padding-bottom: 20px;
        display: flex;
        border-bottom: 1px solid var(--border-color);

        .profile-fit__middle__left {
            width: 50%;
            display: flex;
            flex-direction: column;
            gap: 20px;

            .profile-fit__middle-label {
                width: 80px;
                color: var(--main-text-color);
            }

            .profile-fit__middle-value {
                display: flex;
                flex-direction: column;
                gap: 8px;
                width: calc(100% - 80px);
            }
        }

        .profile-fit__middle__right {
            width: 50%;
        }
    }

    .profile-fit__bottom {
        display: flex;
        .profile-fit__bottom__left {
            // width: 50%;
            .profile-fit__bottom__left__content {
                margin-top: 12px;
                white-space: pre-wrap;
                word-wrap: break-word;
                word-break: break-all;
                font-size: 12px;
                color: var(--main-text-color);
            }
        }
        .profile-fit__bottom__right {
            // width: 50%;
        }
    }
}
