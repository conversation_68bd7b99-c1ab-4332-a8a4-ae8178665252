// 用于首页最新资讯的卡片内容而开发的组件
"use client";

import React from "react";
// import Image from "next/image";
import styles from "./index.module.scss";
import { TextView } from "@/TextView";

export interface ConsultCardProps {
    /** 左侧图片链接 */
    imageUrl: string;
    /** 标题 */
    title: string;
    /** 内容描述 */
    content: string;
    /** 时间 */
    time: string;
}

export function ConsultCard({ imageUrl, title, content, time }: ConsultCardProps) {
    return (
        <div className={styles.card}>
            <div className={styles.imageContainer}>
                <img src={imageUrl} width={80} height={80} className={styles.image} />
            </div>
            <div className={styles.content}>
                <h3 className={styles.title}>
                    <TextView text={title}></TextView>
                </h3>
                <div className={styles.description}>
                    <TextView text={content}></TextView>
                </div>
                <div className={styles.time}>
                    <TextView text={time}></TextView>
                </div>
            </div>
        </div>
    );
}
