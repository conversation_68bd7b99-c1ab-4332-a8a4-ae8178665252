.user-select-wrapper {
    :global {
        .ant-tree-node-content-wrapper {
            max-width: 80%;
            overflow: hidden;
        }
    }
}

.interviewer-list {
        display: grid;
        grid-template-columns: repeat(3, minmax(0, 1fr));
        gap: 12px;
        margin-top: 12px;
        margin-bottom: 20px;
        cursor: pointer;

        .interviewer-item {
            display: flex;
            align-items: center;
            // gap: 12px;
            padding: 8px;
            border-radius: 12px;
            background: var(--bg-color);

            .name {
                color: var(--main-text-color);
            }

            .time {
                font-size: 12px;
                color: var(--sub-text-color);
        }
    }
}


.interviewer-content {
    padding: 8px 16px 0 16px;
    // display: flex;
    // align-items: center;
    // justify-content: center;
}

.interviewer-skeleton {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 12px;
    margin-top: 12px;
    margin-bottom: 20px;

    .skeleton-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px;
        border-radius: 12px;
        background: var(--bg-color);

        .skeleton-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
    }
}