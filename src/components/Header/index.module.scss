.page-header-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;

    &:global(.ant-layout-header) {
        border-bottom: 1px solid #e8e8e8;
        height: 48px;
        line-height: 32px;
        padding: 8px 16px;
    }

    .title-wrapper {
        cursor: pointer;
        .icon-wrapper {
            width: 28px;
            height: 28px;
            background-color: #0000000a;
            border-radius: 4px;
            padding: 6px;
            margin-right: 4px;

            svg {
                width: 16px;
                height: 16px;
            }
        }
        .title {
            font-size: var(--main-text-color);
            font-weight: 600;
            font-size: 16px;
        }
    }
}
