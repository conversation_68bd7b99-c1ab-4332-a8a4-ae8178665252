import { Layout } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import { useNavigate } from "react-router-dom";
import { IconButton } from "@/Button";
import useFullScreenStore from "@/app/store/modules/fullscreen";
import ZoomIcon from "@/app/icons/talentTool/zoom.svg";
import ZoomOutIcon from "@/app/icons/talentTool/zoomout.svg";
import { ReactNode, useEffect } from "react";

export default function Header({
    title,
    containerId,
    children,
    disabled = false,
}: {
    title: string;
    containerId?: string;
    children?: ReactNode;
    disabled?: boolean;
}) {
    const navigate = useNavigate();
    const containerRef = document.getElementById(containerId ?? "");
    const { isFullScreen, setIsFullScreen, toggleFullScreen } = useFullScreenStore((state) => ({
        isFullScreen: state.isFullScreen,
        setIsFullScreen: state.setIsFullScreen,
        toggleFullScreen: state.toggleFullScreen,
    }));

    useEffect(() => {
        // 监听全屏状态变化
        const handleFullscreenChange = async () => {
            if (!document.fullscreenElement) {
                setIsFullScreen(!!document.fullscreenElement);
            }
        };
        document.addEventListener("fullscreenchange", handleFullscreenChange);

        return () => {
            document.removeEventListener("fullscreenchange", handleFullscreenChange);
        };
    }, []);

    return (
        <Layout.Header className={styles["page-header-wrapper"]}>
            <span
                className={styles["title-wrapper"]}
                onClick={() => {
                    if (disabled) return;
                    navigate(-1);
                }}
            >
                <ArrowLeftOutlined className={styles["icon-wrapper"]} />
                <span className={styles["title"]}>{title}</span>
            </span>
            {children}
            {containerId && (
                <div className={styles["header-right"]}>
                    <IconButton
                        icon={isFullScreen ? <ZoomOutIcon /> : <ZoomIcon />}
                        className={styles["more-button"]}
                        onClick={() => toggleFullScreen(containerRef, containerId)}
                        title={isFullScreen ? "退出全屏" : "进入全屏"}
                    />
                </div>
            )}
        </Layout.Header>
    );
}
