import { CloseOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import useRightSidebarStore from "@/app/store/rightSidebar";

export default function RigntSidebar() {
    const { setVisible, header, content, closeCallback } = useRightSidebarStore();
    // const { isCollapsed, toggleCollapse } = useDragSideBar();

    const handleClose = () => {
        setVisible(false);
        // if (isCollapsed) {
        //     toggleCollapse();
        // }
        closeCallback?.();
    };

    return (
        <div className={styles["right-sidebar-wrapper"]}>
            <>
                <div className={`flex-row justify-between items-center p-16 size-20 weight-600 ${styles["header"]}`}>
                    <span>{header}</span>

                    <CloseOutlined onClick={() => handleClose()} />
                </div>
                <div
                    style={{
                        overflow: "auto",
                        height: "calc(100% - 56px)", // 减去header高度
                        padding: "0",
                        display: "flex",
                        flexDirection: "column",
                    }}
                >
                    {content}
                </div>
            </>
        </div>
    );
}
