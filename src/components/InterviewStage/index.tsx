import { Flex, Select } from "antd";
import styles from "./index.module.scss";
import InterviewRightIcon from "@/app/icons/Candidate/InterviewRightIcon.svg";
import { TrackStageStatistic, TrackStateStatistic } from "@/app/store/modules/candidate";
import { useMemo } from "react";

export const InterviewStage = ({
    statusList,
    currentStage,
    handleStatusClick,
}: {
    statusList: TrackStageStatistic[];
    currentStage?: TrackStageStatistic;
    handleStatusClick: (item: TrackStageStatistic) => void;
}) => {
    // 正常面试流程状态列表
    const normalStatusList = useMemo(() => {
        return statusList.filter((item) => item.stageName !== "全部" && item.stageName !== "已终止");
    }, [statusList]);
    // 全部状态
    const allStatus = useMemo(() => {
        return statusList.find((item) => item.stageName === "全部");
    }, [statusList]);
    // 废弃状态
    const discardStatus = useMemo(() => {
        return statusList.find((item) => item.stageName === "已终止");
    }, [statusList]);

    return (
        <div className={styles["candidate-main__status"]}>
            <div className={styles["normal-status-wrapper"]}>
                {normalStatusList.map((item, index) => {
                    return (
                        <div
                            key={item.stageId}
                            className={`${styles["status-item"]} ${currentStage?.stageId === item.stageId ? styles["active"] : ""}`}
                            onClick={() => {
                                handleStatusClick(item);
                            }}
                        >
                            <div className={styles["status-item-count"]}>{item.currentState?.total}</div>
                            <div className={styles["status-item-name"]}>
                                <span>{item.stageName}</span>
                                <Select
                                    value={item.currentState?.stateId}
                                    popupMatchSelectWidth={false}
                                    fieldNames={{
                                        label: "stateName",
                                        value: "stateId",
                                    }}
                                    options={item.trackStateStatistics?.map((item: TrackStateStatistic) => {
                                        return item;
                                    })}
                                    optionRender={(option) => {
                                        return (
                                            <Flex gap={12} justify="space-between" align="center">
                                                <div>{option?.data.stateName}</div>
                                                <div>{option?.data.total}</div>
                                            </Flex>
                                        );
                                    }}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                    }}
                                    onSelect={(_, options) => {
                                        const list = statusList;
                                        list.forEach((item2) => {
                                            if (item2.stageId === item.stageId) {
                                                item2.currentState = options;
                                            }
                                        });
                                        handleStatusClick(item);
                                    }}
                                />
                            </div>
                            {index !== normalStatusList.length - 1 && (
                                <InterviewRightIcon className={styles["status-item-icon"]} />
                            )}
                        </div>
                    );
                })}
            </div>
            <div
                className={`${styles["status-item"]} ${styles["status-item-other"]} ${currentStage?.stageId === allStatus?.stageId ? styles["active"] : ""}`}
                onClick={() => {
                    // setCurrentStage(allStatus);
                    // setAllSearchParams((prev) => ({ ...prev, currentStage: allStatus }));
                    allStatus && handleStatusClick(allStatus);
                }}
            >
                <div className={styles["status-item-count"]}>{allStatus?.total}</div>
                <div className={styles["status-item-name"]}>{allStatus?.stageName}</div>
            </div>
            <div
                className={`${styles["status-item"]} ${styles["status-item-other"]} ${currentStage?.stageId === discardStatus?.stageId ? styles["active"] : ""}`}
                onClick={() => {
                    // setCurrentStage(discardStatus);
                    // setAllSearchParams((prev) => ({ ...prev, currentStage: discardStatus }));
                    discardStatus && handleStatusClick(discardStatus);
                }}
            >
                <div className={styles["status-item-count"]}>{discardStatus?.total}</div>
                <div className={styles["status-item-name"]}>{discardStatus?.stageName}</div>
            </div>
        </div>
    );
};

export default InterviewStage;
