.candidate-main__status {
    margin: 20px 0;
    display: flex;
    align-items: stretch;
    gap: 12px;

    .normal-status-wrapper {
        border-radius: 8px;
        border: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 70%;
    }

    .status-item {
        flex: 1;
        padding: 16px 20px;
        cursor: pointer;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;

        :global {
            .ant-select-selector {
                border: none !important;
                box-shadow: none !important;
                padding: 0;
            }
        }

        &.status-item-other {
            background-color: var(--bg-color);
            border-radius: 8px;
        }

        .status-item-count {
            color: var(--main-text-color);
            font-family: "Kingsoft_Cloud_Font";
            font-size: 21px;
            line-height: 29px;
        }

        .status-item-name {
            color: var(--sub-text-color);
            font-size: 14px;
            line-height: 20px;
            margin-top: 4px;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .status-item-icon {
            position: absolute;
            right: 0;
            top: 25%;
        }

        &.active {
            :global {
                .ant-select-selector {
                    color: var(--primary);
                }
            }
            &::after {
                content: "";
                position: absolute;
                width: 30%;
                height: 3px;
                background-color: var(--primary);
                bottom: 0;
            }

            .status-item-count {
                color: var(--primary);
            }

            .status-item-name {
                color: var(--primary);
            }
        }
    }
}
