import * as React from "react";

import styles from "./index.module.scss";
import { CSSProperties } from "react";
import clsx from "clsx";

export type ButtonType = "primary" | "danger" | null;

export function IconButton(props: {
    onClick?: () => void;
    icon?: JSX.Element;
    type?: ButtonType;
    text?: string;
    bordered?: boolean;
    shadow?: boolean;
    className?: string;
    title?: string;
    disabled?: boolean;
    tabIndex?: number;
    autoFocus?: boolean;
    style?: CSSProperties;
    iconStyle?: CSSProperties;
    aria?: string;
    textStyle?: CSSProperties;
}) {
    return (
        <button
            className={clsx(
                "clickable",
                styles["icon-button"],
                {
                    [styles.border]: props.bordered,
                    [styles.shadow]: props.shadow,
                },
                styles[props.type ?? ""],
                props.className
            )}
            onClick={props.onClick}
            title={props.title}
            disabled={props.disabled}
            role="button"
            tabIndex={props.tabIndex}
            autoFocus={props.autoFocus}
            style={props.style}
            aria-label={props.aria}
        >
            {props.icon && (
                <div
                    aria-label={props.text || props.title}
                    className={clsx(styles["icon-button-icon"], {
                        "no-dark": props.type === "primary",
                    })}
                    style={props.iconStyle}
                >
                    {props.icon}
                </div>
            )}

            {props.text && (
                <div
                    aria-label={props.text || props.title}
                    className={styles["icon-button-text"]}
                    style={props.textStyle}
                >
                    {props.text}
                </div>
            )}
        </button>
    );
}
