.tab-container {
    padding: 0;
    margin-bottom: 0;
    background-color: #f5f5f5;

    .custom-tabs {
        margin: 0;

        // 自定义标签页样式
        :global(.ant-tabs-nav) {
            margin: 0;
            background-color: #f5f5f5;

            &::before {
                border-bottom: none;
            }
        }

        :global(.ant-tabs-tab) {
            // background-color: #e8e8e8;
            border-color: #d9d9d9;
            color: rgba(0, 0, 0, 0.65);
            margin-right: 2px;
            border-radius: 8px 8px 0 0;
            padding: 8px 16px;
            min-width: 120px;

            &:hover {
                background-color: #f0f0f0;
                color: rgba(0, 0, 0, 0.85);
            }

            .ant-tabs-tab-btn {
                display: flex;
                align-items: center;
                gap: 6px;
                font-size: 14px;

                svg {
                    width: 16px;
                    height: 16px;
                    vertical-align: middle;
                }
            }
        }

        :global(.ant-tabs-tab-active) {
            background-color: #ffffff !important;
            color: rgba(0, 0, 0, 0.85) !important;
            font-weight: 500;
            border-color: #d9d9d9;

            .ant-tabs-tab-btn {
                color: rgba(0, 0, 0, 0.85) !important;
            }
        }

        :global(.ant-tabs-content-holder) {
            display: none; // 隐藏内容区域，因为我们只需要标签页导航
        }

        :global(.ant-tabs-ink-bar) {
            display: none; // 隐藏下划线指示器
        }

        :global(.ant-tabs-tab-btn) {
            text-shadow: unset !important;
            display: flex;
        }
        :global(.ant-tabs-tab-icon) {
            align-items: center;
            justify-content: center;
            display: flex;
        }
    }
}