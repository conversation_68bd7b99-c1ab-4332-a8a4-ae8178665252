import React from "react";
import { Tabs } from "antd";
import { useNavigate } from "react-router-dom";
import { Path } from "@/app/constant";
import styles from "./index.module.scss";
import InterviewTimeIcon from "@/app/icons/sidebar/interview-time.svg";
import MyInterviewIcon from "@/app/icons/sidebar/my-interview.svg";
import InterviewDashboardIcon from "@/app/icons/sidebar/interview-dashboard.svg";

export interface BreadCrumbItem {
    title: string;
    path?: string;
    key: string;
    icon?: React.ReactNode;
}

export interface BreadCrumbProps {
    items?: BreadCrumbItem[];
    className?: string;
    // 当前页面路径，用于确定当前在哪个页面
    currentPath?: string;
    // 父页面路径，用于返回上一级
    parentPath?: string;
    // 父页面标题
    parentTitle?: string;
    // 当前页面标题
    currentTitle?: string;
}

type TabItem = {
    key: string;
    label: React.ReactNode;
    icon?: React.ReactNode;
};

// 获取页面图标的辅助函数
const getPageIcon = (path: string): React.ReactNode => {
    switch (path) {
        case Path.Interviewer:
            return <InterviewDashboardIcon />;
        case Path.InterviewTime:
            return <InterviewTimeIcon />;
        case Path.MyInterview:
            return <MyInterviewIcon />;
        default:
            return null;
    }
};

const BreadCrumb: React.FC<BreadCrumbProps> = ({
    items,
    className,
    currentPath,
    parentPath = Path.Interviewer,
    parentTitle = "面试官",
    currentTitle,
}) => {
    const navigate = useNavigate();

    // 如果传入了自定义items，则使用自定义items
    const getTabItems = (): TabItem[] => {
        if (items) {
            return items.map((item) => ({
                key: item.key,
                label: item.title,
                icon: item.icon,
            }));
        }

        // 如果没有传入currentPath或currentTitle，则不显示标签页
        if (!currentPath || !currentTitle) {
            return [];
        }

        // 构建标签页项
        return [
            {
                key: parentPath,
                label: parentTitle,
                icon: getPageIcon(parentPath),
            },
            {
                key: currentPath,
                label: currentTitle,
                icon: getPageIcon(currentPath),
            },
        ];
    };

    const tabItems = getTabItems();

    // 如果没有标签页项，则不显示标签页
    if (tabItems.length === 0) {
        return null;
    }

    const handleTabChange = (activeKey: string) => {
        navigate(activeKey);
    };

    return (
        <div className={`${styles["tab-container"]} ${className || ""}`}>
            <Tabs
                activeKey={currentPath}
                onChange={handleTabChange}
                items={tabItems}
                className={styles["custom-tabs"]}
                size="small"
                type="card"
            />
        </div>
    );
};

export default BreadCrumb;
