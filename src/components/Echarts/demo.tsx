import React from "react";
import EChart from "./index";
import type { EChartsOption } from "echarts";

// 折线图配置示例
const LineChartDemo: React.FC = () => {
    const option: EChartsOption = {
        title: {
            text: "招聘趋势",
        },
        tooltip: {
            trigger: "axis",
        },
        xAxis: {
            type: "category",
            data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        },
        yAxis: {
            type: "value",
        },
        series: [
            {
                data: [150, 230, 224, 218, 135, 147, 260],
                type: "line",
            },
        ],
    };

    return (
        <div style={{ height: "400px" }}>
            <EChart option={option} />
        </div>
    );
};

// 柱状图配置示例
const BarChartDemo: React.FC = () => {
    const option: EChartsOption = {
        title: {
            text: "部门招聘统计",
        },
        tooltip: {
            trigger: "axis",
            axisPointer: {
                type: "shadow",
            },
        },
        xAxis: {
            type: "category",
            data: ["研发", "销售", "运营", "市场", "人事"],
        },
        yAxis: {
            type: "value",
        },
        series: [
            {
                data: [120, 200, 150, 80, 70],
                type: "bar",
            },
        ],
    };

    return (
        <div style={{ height: "400px" }}>
            <EChart option={option} />
        </div>
    );
};

// 饼图配置示例
const PieChartDemo: React.FC = () => {
    const option: EChartsOption = {
        title: {
            text: "招聘职位分布",
            left: "center",
        },
        tooltip: {
            trigger: "item",
        },
        legend: {
            orient: "vertical",
            left: "left",
        },
        series: [
            {
                name: "职位分布",
                type: "pie",
                radius: "50%",
                data: [
                    { value: 1048, name: "前端开发" },
                    { value: 735, name: "后端开发" },
                    { value: 580, name: "产品经理" },
                    { value: 484, name: "UI设计" },
                    { value: 300, name: "运维" },
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: "rgba(0, 0, 0, 0.5)",
                    },
                },
            },
        ],
    };

    return (
        <div style={{ height: "400px" }}>
            <EChart option={option} />
        </div>
    );
};

// 使用示例
const ChartDemo: React.FC = () => {
    return (
        <div style={{ padding: "20px" }}>
            <h2>图表示例</h2>
            <LineChartDemo />
            <div style={{ height: "20px" }} />
            <BarChartDemo />
            <div style={{ height: "20px" }} />
            <PieChartDemo />
        </div>
    );
};

export default ChartDemo;
