import { useEffect, useRef } from "react";
import * as echarts from "echarts";
import type { EChartsOption } from "echarts";
import { debounce } from "lodash";

interface EChartProps {
    option: EChartsOption;
    style?: React.CSSProperties;
    className?: string;
    theme?: "light" | "dark";
    loading?: boolean;
    onEvents?: Record<string, () => void>;
}

const ECharts: React.FC<EChartProps> = ({ option, style, className, theme = "light", loading = false, onEvents }) => {
    const chartRef = useRef<HTMLDivElement>(null);
    const chartInstance = useRef<echarts.ECharts | null>(null);

    // 初始化图表
    const initChart = () => {
        const el = chartRef.current;
        if (!el) return;

        chartInstance.current = echarts.init(el, theme);

        // 绑定事件
        if (onEvents && chartInstance.current) {
            Object.entries(onEvents).forEach(([eventName, handler]) => {
                chartInstance.current?.on(eventName, handler as any);
            });
        }
    };

    // 更新图表配置
    const updateChart = () => {
        if (!chartInstance.current) {
            initChart();
        }

        if (chartInstance.current) {
            loading ? chartInstance.current.showLoading() : chartInstance.current.hideLoading();
            chartInstance.current.setOption(option, true);
        }
    };

    // 处理窗口大小变化
    const handleResize = debounce(() => {
        chartInstance.current?.resize();
    }, 100);

    useEffect(() => {
        initChart();

        return () => {
            chartInstance.current?.dispose();
            chartInstance.current = null;
        };
    }, [theme]);

    useEffect(() => {
        updateChart();
    }, [option, loading]);

    useEffect(() => {
        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    return (
        <div
            ref={chartRef}
            className={className}
            style={{
                width: "100%",
                height: "100%",
                ...style,
            }}
        />
    );
};

export default ECharts;
