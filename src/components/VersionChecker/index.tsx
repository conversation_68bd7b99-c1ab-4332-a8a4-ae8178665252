"use client";

import { useState, useEffect } from "react";
import { <PERSON>ton, Flex, Modal } from "antd";
import styles from "./index.module.scss";

const VersionChecker = ({ checkInterval = 30000 }) => {
    const [hasUpdate, setHasUpdate] = useState(false);
    const [clientBuildId, setClientBuildId] = useState("");
    const [serverBuildId, setServerBuildId] = useState("");

    // 获取客户端当前版本
    useEffect(() => {
        if (process.env.NODE_ENV === "development") return;

        if (typeof window !== "undefined") {
            const clientBuildId = localStorage.getItem("version") ?? "";
            setClientBuildId(clientBuildId);
        }
    }, []);

    // 检查版本更新
    const checkForUpdates = async () => {
        setHasUpdate(false);

        try {
            const res = await fetch("/api/versionId", {
                cache: "no-store", // 禁用缓存，确保获取最新版本
            });

            const result = await res.json();
            const { buildId: serverBuildId } = result;
            // 浏览器不存在版本号时，默认获取服务端版本号
            setServerBuildId(serverBuildId);
            if (!clientBuildId) {
                if (typeof window !== "undefined") {
                    localStorage.setItem("version", serverBuildId);
                }
                setClientBuildId(serverBuildId);
            }
            // 对比客户端与服务器版本
            if (clientBuildId && serverBuildId && serverBuildId !== clientBuildId) {
                setHasUpdate(true);
            }
        } catch (error) {
            console.error("版本检测失败:", error);
        }
    };

    // 初始检查 + 定时检查
    useEffect(() => {
        if (process.env.NODE_ENV === "development") return;
        checkForUpdates(); // 组件挂载时立即检查
        const interval = setInterval(checkForUpdates, checkInterval);
        return () => clearInterval(interval);
    }, [clientBuildId, checkInterval]);

    // 强制刷新页面
    const handleRefresh = () => {
        setClientBuildId(serverBuildId);
        window.location.reload(); // 重新加载
    };

    if (!hasUpdate) return null;

    // 更新提示弹窗
    return (
        <Modal
            open={hasUpdate}
            width={"30%"}
            title="版本更新"
            maskClosable={false}
            confirmLoading={false}
            closable={false}
            footer={
                <Button type="primary" onClick={handleRefresh}>
                    立即刷新
                </Button>
            }
        >
            <Flex vertical className={styles["version-checker"]}>
                <div className={styles["title"]}>检测到新版本，请刷新页面</div>
                <div className={styles["text"]}>
                    当前版本: <span className={styles["version"]}>{clientBuildId}</span>
                </div>
                <div className={styles["text"]}>
                    最新版本: <span className={styles["version"]}>{serverBuildId}</span>
                </div>
            </Flex>
        </Modal>
    );
};

export default VersionChecker;
