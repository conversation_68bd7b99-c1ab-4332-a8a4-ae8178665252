import { Empty, Flex, Input, Tree } from "antd";
import SearchIcon from "@/app/icons/SearchIcon.svg";
import styles from "./index.module.scss";
import { Key, ReactNode, useEffect, useState } from "react";
import { debounce } from "lodash-es";
import { arrayToTree, getNodeWithAncestors } from "@/app/utils";
// import { recruitmentType } from "@/app/constant";

const ChatTree = ({
    defaultKey = "id",
    parentKey = "parentId",
    fieldNames,
    defaultFlatTreeData,
    defaultTreeData,
    currentNode,
    placeholder = "请搜索...",
    handleNodeClick,
    titleRender,
    children,
}: {
    defaultKey?: string; // 唯一标识
    parentKey?: string; // 父级唯一标识
    fieldNames?: { title?: string; key?: string; children?: string }; // 格式化树形数据
    defaultFlatTreeData: any[]; // 格式化后的树形数据
    defaultTreeData: any[]; // 原始树形数据
    currentNode?: any; // 当前选中的节点
    placeholder?: string; // 搜索框占位符
    handleNodeClick: (node: any) => void; // 点击节点回调
    titleRender?: (node: any) => ReactNode; // 节点自定义渲染
    children?: ReactNode;
    mode?: string;
}) => {
    const [searchValue, setSearchValue] = useState("");
    const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
    const [selectedKeys, setSelectedKeys] = useState<Key[]>([]);
    const [treeData, setTreeData] = useState<any[]>([]);

    useEffect(() => {
        const handleDebounce = debounce(() => {
            // 预先构建子节点到父节点的映射
            const childToParentMap = new Map();
            defaultFlatTreeData.forEach((parent) => {
                if (parent.children) {
                    parent.children.forEach((child: any) => {
                        childToParentMap.set(child[defaultKey], parent);
                    });
                }
            });
            // 查找父节点信息
            const getParentNodes = (node: any): any[] => {
                const result = [];
                let current = node;
                const condition = true;

                while (condition) {
                    const parent = childToParentMap.get(current[defaultKey]);
                    if (!parent) break;
                    result.push(parent);
                    current = parent;
                }

                return result;
            };

            const filterTreeData = defaultFlatTreeData.filter((item: any) =>
                item.dept_name.toUpperCase().includes(searchValue.toUpperCase())
            );

            // 使用Map去重和存储节点
            const nodeMap = new Map();
            filterTreeData.forEach((item: any) => {
                nodeMap.set(item[defaultKey], item);
                const parentNodes = getParentNodes(item);
                parentNodes.forEach((parent) => {
                    nodeMap.set(parent[defaultKey], parent);
                });
            });

            // 转换回数组并排序
            const nodeData = Array.from(nodeMap.values());
            nodeData.sort((prev: any, cur: any) => (Number(prev[defaultKey]) <= Number(cur[defaultKey]) ? -1 : 1));
            const treeData = arrayToTree(nodeData, defaultKey, parentKey);
            const exists = nodeData.findIndex((item) => item[defaultKey] === currentNode?.[defaultKey]);
            setTreeData(treeData ?? defaultTreeData);

            if (exists === -1) {
                if (nodeData.length > 0) {
                    // 过滤后的数据中没有缓存数据
                    if (searchValue) {
                        const treeNodeList = getNodeWithAncestors(
                            defaultFlatTreeData,
                            filterTreeData?.[0]?.[defaultKey],
                            defaultKey
                        );
                        setExpandedKeys(treeNodeList?.map((item) => item[defaultKey]));
                    } else {
                        setSelectedKeys([nodeData?.[0]?.dept_code]);
                        setExpandedKeys([nodeData?.[0]?.dept_code]);
                        handleNodeClick(nodeData?.[0]);
                    }
                }
            } else {
                // 当已存在缓存数据，默认直接展示
                if (currentNode) {
                    setSelectedKeys([currentNode?.[defaultKey] ?? ""]);
                    const treeNodeList = getNodeWithAncestors(
                        defaultFlatTreeData,
                        currentNode?.[defaultKey],
                        defaultKey
                    );
                    setExpandedKeys(treeNodeList?.map((item) => item[defaultKey]));
                }
            }
        }, 300);

        handleDebounce();

        return () => {
            handleDebounce.cancel();
        };
    }, [defaultTreeData, searchValue]);

    const searchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        setSearchValue(value);
    };

    const onExpand = (expandedKeysValue: Key[]) => {
        setExpandedKeys(expandedKeysValue);
    };

    const onSelect = (selectedKeysValue: Key[], { selectedNodes }: { selectedNodes: any[] }) => {
        if (selectedNodes.length === 0) return;

        setSelectedKeys(selectedKeysValue);
        handleNodeClick(selectedNodes?.[0]);
    };

    return (
        <div>
            {/* <Segmented block defaultValue={1} options={recruitmentType} /> */}
            <div className={styles["chat-tree"]}>
                <Flex gap={16} align="center">
                    <Input
                        className="search-input-wrapper"
                        value={searchValue}
                        placeholder={placeholder}
                        prefix={<SearchIcon />}
                        allowClear
                        onChange={searchChange}
                    />
                    {children}
                </Flex>
                {treeData.length > 0 ? (
                    <Tree
                        defaultExpandAll
                        blockNode
                        treeData={treeData}
                        fieldNames={fieldNames}
                        expandedKeys={expandedKeys}
                        selectedKeys={selectedKeys}
                        onSelect={onSelect}
                        onExpand={onExpand}
                        titleRender={titleRender}
                    />
                ) : (
                    <Empty />
                )}
            </div>
        </div>
    );
};

export default ChatTree;
