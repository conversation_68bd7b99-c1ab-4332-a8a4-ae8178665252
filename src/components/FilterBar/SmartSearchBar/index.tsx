import { Input, Button } from "antd";
import { useEffect, useState } from "react";
import styles from "./index.module.scss";
import SearchIcon from "@/app/icons/SearchIcon.svg";

/** 智能搜索栏组件属性接口 */
interface SmartSearchBarProps {
    width?: number;
    /** 搜索值 */
    value?: string;
    /** 占位符 */
    placeholder?: string;
    /** 搜索回调 */
    onSearch?: (keyword: string) => void;
}

/**
 * 智能搜索栏组件
 * 支持普通搜索和AI智能搜索
 */
const SmartSearchBar: React.FC<SmartSearchBarProps> = ({
    width,
    value = "",
    placeholder = "智能搜索，告诉我你想要查询的内容",
    onSearch,
}) => {
    const [searchValue, setSearchValue] = useState(value);

    // 同步外部value变化
    useEffect(() => {
        setSearchValue(value);
    }, [value]);

    const handleSearch = () => {
        onSearch?.(searchValue.trim());
    };

    /** 处理输入变化 */
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchValue(e.target.value);
    };

    /** 处理回车键搜索 */
    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === "Enter") {
            handleSearch();
        }
    };

    /** 处理清空搜索 */
    const handleClear = () => {
        setSearchValue("");
        onSearch?.("");
    };

    return (
        <div className={styles["smart-search-bar"]}>
            <Input
                style={{ width }}
                className={styles["smart-search"]}
                placeholder={placeholder}
                prefix={<SearchIcon />}
                value={searchValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyPress}
                allowClear
                onClear={handleClear}
            />
        </div>
    );
};

export default SmartSearchBar;
