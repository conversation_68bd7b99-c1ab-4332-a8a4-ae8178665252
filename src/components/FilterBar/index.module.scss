.filter-section {
    .filter-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .filter-left {
            display: flex;
            align-items: center;
            gap: 8px;

            .collapse-button {
                border: none;
                box-shadow: none;
                background: #f5f5f5;
                border-radius: 6px;
                height: 32px;
                padding: 0 12px;
                color: #202223;

                &:hover {
                    background: #e8e8e8;
                }
            }

            .auto-save-button {
                border: none;
                box-shadow: none;
                background: #f5f5f5;
                border-radius: 6px;
                height: 32px;
                width: 32px;
                padding: 0;
                color: #666;

                &:hover {
                    background: #e6f7ff;
                    color: #1890ff;
                }
            }
        }

        .filter-right {
            display: flex;
            align-items: center;
            gap: 8px;

            .smart-search {
                flex: 1;
                width: 360px;
                position: relative;
                background:
                    linear-gradient(white, white) padding-box,
                    linear-gradient(to right, #0099f2, #843dff, #33d1cc) border-box;
                border: 2px solid transparent;
                border-radius: 6px;
            }
        }
    }
    .search-row {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        .filter-item {
            :global(.ant-select-selector) {
                border: none;
                background-color: #f5f5f5;
            }
            :global(.ant-select-prefix) {
                color: rgba(0, 0, 0, 0.42);
            }
        }
        //
        .search-actions {
            display: flex;
            align-items: center;
            gap: 8px;

            .action-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                background: white;
                cursor: pointer;
                transition: all 0.2s;

                &:hover {
                    border-color: #1890ff;
                    color: #1890ff;
                }
            }
        }
    }

    // 筛选表单样式
    .filter-form {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        padding: 16px 0;
    }

    .filter-item {
        display: flex;
        flex-direction: column;
        gap: 8px;
        min-width: 120px;

        &--range {
            min-width: 200px;
        }
    }

    .filter-label {
        font-size: 12px;
        font-weight: 500;
        color: #666666;
        line-height: 1.2;
    }

    .filter-selector {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        background: #f8f9fa;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;
        min-height: 36px;

        &:hover {
            border-color: #1890ff;
            background: #f0f8ff;
        }
    }

    .filter-content {
        font-size: 13px;
        color: #333333;
        flex: 1;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .filter-arrow {
        font-size: 10px;
        color: #999999;
        margin-left: 8px;
        flex-shrink: 0;
    }

    .filter-range {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .filter-range-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        background: #f8f9fa;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        min-width: 80px;
        min-height: 32px;

        &:hover {
            border-color: #1890ff;
            background: #f0f8ff;
        }

        .filter-content {
            font-size: 12px;
        }

        .filter-arrow {
            font-size: 8px;
            margin-left: 4px;
        }

        .filter-range-separator {
            font-size: 12px;
            color: #666666;
            margin: 0 4px;
        }
    }
}
