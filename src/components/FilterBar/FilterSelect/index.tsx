import React, { ReactNode } from "react";
import { Select } from "antd";
import { CaretDownOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import type { SelectProps } from "antd";

type TagRender = SelectProps["tagRender"];

/** 筛选选择器组件属性接口 */
interface FilterSelectProps {
    /** 标签文本 */
    label?: string;
    /** 选择器值 */
    value?: string | string[];
    /** 选项列表 */
    options?: Array<{ label: string; value: string }>;
    /** 选择模式 */
    mode?: "tags" | "multiple" | undefined;
    /** 占位符 */
    placeholder?: string;
    /** 宽度 */
    width?: number;
    /** 允许清空 */
    allowClear?: boolean | { clearIcon?: ReactNode };
    /** 值变化回调 */
    onChange?: (value: string | string[]) => void;
}

/**
 * 筛选选择器组件
 * 可复用的带标签的选择器
 */
const FilterSelect: React.FC<FilterSelectProps> = (props) => {
    const { label, value, placeholder = "", width = 120, allowClear = true, mode, onChange } = props;

    /**
     * 自定义标签渲染函数
     * 在标签之间添加逗号分隔，最后一个标签不添加逗号
     * @param props 标签属性
     * @returns 渲染的标签React元素
     */
    const tagRender: TagRender = (props) => {
        // 获取当前选中的值数组
        const selectedValues = Array.isArray(value) ? value : [];
        // 获取当前标签在数组中的索引
        const currentIndex = selectedValues.indexOf(props.value as string);
        // 判断是否为最后一个标签
        const isLastTag = currentIndex === selectedValues.length - 1;

        // 返回标签React元素，最后一个标签不添加逗号
        return (
            <span>
                {props.label}
                {isLastTag ? "" : ","}
            </span>
        );
    };
    return (
        <div className={styles["filter-select-wrapper"]}>
            <Select
                {...props}
                onChange={onChange}
                allowClear={allowClear}
                prefix={label}
                mode={mode}
                style={{ minWidth: width }}
                tagRender={tagRender}
                value={value}
                placeholder={placeholder}
                suffixIcon={<CaretDownOutlined />}
            />
        </div>
    );
};

export default FilterSelect;
