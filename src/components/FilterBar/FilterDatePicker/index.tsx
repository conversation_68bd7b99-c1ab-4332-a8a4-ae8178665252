import React from "react";
import { DatePicker } from "antd";
import type { Dayjs } from "dayjs";
import styles from "./index.module.scss";

const { RangePicker } = DatePicker;

/** 筛选日期选择器组件属性接口 */
interface FilterDatePickerProps {
    separator?: React.ReactNode;
    /** 标签文本 */
    label?: string;
    /** 选择器值 - 日期范围数组 */
    value?: [Dayjs | null, Dayjs | null] | null;
    /** 占位符 - 日期范围的开始和结束占位符 */
    placeholder?: [string, string];
    /** 宽度 */
    width?: number;
    /** 值变化回调 */
    onChange?: (dates: [Dayjs | null, Dayjs | null] | null, dateStrings: [string, string]) => void;
}

/**
 * 筛选日期选择器组件
 * 可复用的带标签的日期范围选择器
 */
const FilterDatePicker: React.FC<FilterDatePickerProps> = (props) => {
    const { label, value, placeholder = ["开始日期", "结束日期"], width = 100, onChange, separator } = props;

    return (
        <div className={styles["filter-select-wrapper"]}>
            <RangePicker
                prefix={label}
                style={{ minWidth: width }}
                value={value}
                placeholder={placeholder}
                onChange={onChange}
                separator={separator}
            />
        </div>
    );
};

export default FilterDatePicker;
