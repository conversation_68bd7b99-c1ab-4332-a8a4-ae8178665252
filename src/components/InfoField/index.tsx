import React from "react";
import styles from "./index.module.scss";
import InfoFieldGroup from "@/InfoFieldGroup";

/** InfoField组件属性接口 */
interface InfoFieldProps {
    /** 标签内容 */
    label: React.ReactNode | string;
    /** 标签宽度 */
    labelWidth?: number;
    /** 值内容 */
    value: React.ReactNode | string;
}
/**
 * 信息字段组件
 * @param props InfoField组件属性
 * @param props.label 标签内容，可以是字符串或React节点
 * @param props.labelWidth 标签宽度
 * @param props.value 值内容，可以是字符串或React节点
 * @returns InfoField组件
 */
export default function InfoField({ label, labelWidth, value }: InfoFieldProps) {
    return (
        <div className={styles["field-wrapper"]}>
            <div className={styles["field-label"]} style={{ width: labelWidth }}>
                {label}
            </div>
            <div className={styles["field-value"]}>{value}</div>
        </div>
    );
}

export { InfoFieldGroup };
