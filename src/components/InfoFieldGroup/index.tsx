import React from "react";
import { MinusOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";

/** InfoFieldGroup组件属性接口 */
interface InfoFieldGroupProps {
    /** 第一个InfoField的图标 */
    firstIcon?: React.ReactNode;
    /** 子组件 */
    children: React.ReactNode;
    /** 间距 */
    gap?: number;
}

/**
 * 信息字段组合组件
 * @param props InfoFieldGroup组件属性
 * @param props.firstIcon 第一个InfoField的图标，默认为MinusOutlined
 * @param props.children 子组件，应该包含两个InfoField组件
 * @returns InfoFieldGroup组件
 */
export default function InfoFieldGroup({ firstIcon, children, gap = 4 }: InfoFieldGroupProps) {
    const childrenArray = React.Children.toArray(children);
    const firstChild = childrenArray[0];
    const secondChild = childrenArray[1];

    return (
        <div
            className={styles["info-field-group"]}
            style={
                {
                    gap,
                    "--dynamic-gap": `${gap}px`,
                } as React.CSSProperties & { "--dynamic-gap": string }
            }
        >
            {/* 第一个InfoField */}
            <div className={styles["field-item"]}>
                <div style={{ flexShrink: 0 }}>{firstIcon}</div>
                {firstChild}
            </div>

            {/* 第二个InfoField，带虚线连接 */}
            <div className={`${styles["field-item"]} ${styles["field-item-with-line"]}`}>
                <DefaultLastIcon />
                {secondChild}
            </div>
        </div>
    );
}

function DefaultLastIcon() {
    return (
        <div className={styles["last-icon-wrapper"]}>
            <i className={styles["icon-dot"]}></i>
        </div>
    );
}
