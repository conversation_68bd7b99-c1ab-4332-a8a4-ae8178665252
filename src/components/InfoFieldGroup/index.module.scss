.info-field-group {
    display: flex;
    flex-direction: column;
    /* gap 通过内联样式动态设置 */

    .field-item {
        display: flex;
        align-items: center;
        gap: 8px;
        position: relative;

        // 图标样式
        .anticon {
            flex-shrink: 0;
            color: rgba(0, 0, 0, 0.6);
        }
    }

    .field-item-with-line {
        position: relative;
        // 使用::before伪类创建虚线连接
        &::before {
            content: "";
            position: absolute;
            left: 6px;
            top: calc(-1 * var(--dynamic-gap) - 4px);
            width: 1px;
            height: calc(8px + var(--dynamic-gap));
            border-left: 1px dashed #d9d9d9;
            z-index: 1;
        }
    }
}

.last-icon-wrapper {
    width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-dot {
        width: 4px;
        height: 4px;
        background-color: #626262;
        border-radius: 50%;
    }
}
