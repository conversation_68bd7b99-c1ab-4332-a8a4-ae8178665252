.rich-text-editor {
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    background: #fff;

    :global {
        .quill {
            height: 100% !important;
            display: flex;
            flex-direction: column;
        }

        .ql-toolbar.ql-snow {
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            border: 1px solid #d9d9d9;
            border-bottom: none;
            padding: 8px;

            .ql-formats {
                margin-right: 12px;
            }
        }

        .ql-container.ql-snow {
            flex: 1;
            border: 1px solid #d9d9d9;
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
            font-size: 14px;
            line-height: 1.5;
            height: calc(100% - 42px);
        }

        .ql-editor {
            min-height: 120px;
            padding: 12px 15px;

            &:focus {
                outline: none;
            }

            p {
                margin: 0;
                padding: 0;
            }
        }

        // 自定义滚动条样式
        .ql-editor::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .ql-editor::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;

            &:hover {
                background: rgba(0, 0, 0, 0.3);
            }
        }

        .ql-editor::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
        }

        // 只读模式样式
        .ql-disabled {
            .ql-editor {
                background-color: #f5f5f5;
                cursor: not-allowed;
            }
        }

        // 占位符样式
        .ql-editor.ql-blank::before {
            font-style: normal;
            color: #bfbfbf;
        }
    }
}
