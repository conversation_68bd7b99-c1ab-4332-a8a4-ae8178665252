import React from "react";
import { Spin } from "antd";
import classNames from "classnames";
import styles from "./index.module.scss";

interface LoadingProps {
    loading?: boolean; // 是否显示加载
    fullScreen?: boolean; // 是否全屏显示
    tip?: string; // 加载提示文字
    size?: "small" | "default" | "large"; // 加载图标大小
    delay?: number; // 延迟显示加载效果的时间（毫秒）
    className?: string; // 自定义类名
    style?: React.CSSProperties; // 自定义样式
    maskColor?: string; // 遮罩层颜色
    children?: React.ReactNode; // 子元素
}

const Loading: React.FC<LoadingProps> = ({
    loading = true,
    fullScreen = false,
    tip = "加载中...",
    size = "default",
    delay = 0,
    className,
    style,
    maskColor,
    children,
}) => {
    if (!loading && !children) {
        return null;
    }

    const loadingNode = (
        <div
            className={classNames(
                styles.loadingWrapper,
                {
                    [styles.fullScreen]: fullScreen,
                    [styles.hasChildren]: !!children,
                },
                className
            )}
            style={{
                ...style,
                backgroundColor: maskColor,
            }}
        >
            <Spin size={size} tip={tip} delay={delay} className={styles.spin} />
        </div>
    );

    if (children) {
        return (
            <div className={styles.container}>
                {children}
                {loading && loadingNode}
            </div>
        );
    }

    return loadingNode;
};

export default Loading;
