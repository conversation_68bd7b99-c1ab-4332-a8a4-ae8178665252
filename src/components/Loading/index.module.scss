.container {
    position: relative;
    width: 100%;
    height: 100%;
}

.loadingWrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.65);
    transition: all 0.3s;

    &.fullScreen {
        position: fixed;
        z-index: 9999;
    }

    &.hasChildren {
        position: absolute;
    }

    .spin {
        :global {
            .ant-spin-text {
                margin-top: 8px;
                font-size: 14px;
                color: #1890ff;
            }
            
            .ant-spin-dot {
                i {
                    background-color: #1890ff;
                }
            }
        }
    }
}

// 示例用法：
/*
    <Loading loading={true} fullScreen={true} tip="加载中..." />

    <Loading loading={true}>
        <div>内容区域</div>
    </Loading>

    <Loading loading={true} maskColor="rgba(0, 0, 0, 0.3)">
        <div>深色遮罩的内容区域</div>
    </Loading>
*/
