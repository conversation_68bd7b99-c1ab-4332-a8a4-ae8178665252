.attach-images {
    position: absolute;
    left: 12px;
    top: 8px;
    display: flex;
    gap: 12px;
    max-width: 97%;
    overflow: auto;
    padding-bottom: 4px;

    &::-webkit-scrollbar {
        --bar-width: 8px;
    }
  }
  
  .attach-image {
    cursor: default;
    width: 64px;
    height: 64px;
    border: rgba($color: #888, $alpha: 0.2) 1px solid;
    border-radius: 5px;
    margin-right: 10px;
    background-size: cover;
    background-position: center;
    background-color: var(--white);
   

    .attach-image-mask {
      width: 100%;
      height: 100%;
      opacity: 0;
      transition: all ease 0.2s;
    }
  
    .attach-image-mask:hover {
      opacity: 1;
    }
  
  }
  
  .chat-input-panel-inner {
    cursor: text;
    display: flex;
    flex: 1;
    position: relative;
    box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.10);
    border-radius: 16px;
    border: 1.5px solid #E8E8E8;
    opacity: 0.99;
  }
  
  .chat-input-panel-inner-attach {
    padding-top: 80px;
  }
  
  .chat-input-panel-inner:has(.chat-input:focus) {
    // border: 1px solid var(--primary);
  }
  
  .chat-input {
    height: 100%;
    width: 100%;
    // border-radius: 10px;
    border: none;
    // box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.03);
    background-color: var(--white);
    color: var(--black);
    font-family: inherit;
    padding: 14px 12px 52px;
    resize: none;
    outline: none;
    box-sizing: border-box;
    min-height: 68px;

    border-radius: 16px;
  }
  
  .bottom-box {
    padding: 0 12px;
    position: absolute;
    height: 32px;
    left: 0;
    right: 0;
    bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .content-right {
        display: flex;
        align-items: center;
    }

    .enter-icon {
        height: 32px;
        width: 32px;
        padding: 8px;
        border-radius: 12px;
        border: 1px solid #ebebeb;
        background: #e8e8e8;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;  
        cursor: not-allowed;
        margin-left: 6px;

        svg path {
            fill: black;
        }
    }
    .enter-icon-active {
        background: #0099F2;
        border: 1px solid #0099F2;
        cursor: pointer;
        
        svg path {
            fill: #FFFFFF;
        }
    }
 }
  
  .chat-input-send {
    background-color: var(--primary);
    color: white;
    position: absolute;
    right: 30px;
    bottom: 32px;
  }
  
  @media only screen and (max-width: 600px) {
    .chat-input {
      font-size: 16px;
    }
  
    .chat-input-send {
      bottom: 30px;
    }
  }

  .attached-file-item-wrapper {
    position: relative;

    .delete-btn {
        width: 24px;
        height: 24px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        float: right;
        background-color: var(--white);
        position: absolute;
        right: 8px;
        top: 4px;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    &:hover {
      .delete-btn {
        opacity: 1;
      }
    }
  }