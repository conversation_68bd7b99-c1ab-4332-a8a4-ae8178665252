export const OWNER = "ChatGPTNextWeb";
export const REPO = "ChatGPT-Next-Web";
export const REPO_URL = `https://github.com/${OWNER}/${REPO}`;
export const ISSUE_URL = `https://github.com/${OWNER}/${REPO}/issues`;
export const UPDATE_URL = `${REPO_URL}#keep-updated`;
export const RELEASE_URL = `${REPO_URL}/releases`;
export const FETCH_COMMIT_URL = `https://api.github.com/repos/${OWNER}/${REPO}/commits?per_page=1`;
export const FETCH_TAG_URL = `https://api.github.com/repos/${OWNER}/${REPO}/tags?per_page=1`;
export const RUNTIME_CONFIG_DOM = "danger-runtime-config";

export const DEEPSEEK_BASE_URL = "https://api.deepseek.com";

export const CACHE_URL_PREFIX = "/api/cache";
export const UPLOAD_URL = `${CACHE_URL_PREFIX}/upload`;

export enum Path {
    Home = "/",
    Chat = "/chat",
    Settings = "/settings",
    NewChat = "/new-chat",
    Auth = "/auth",
    Artifacts = "/artifacts",
    SearchChat = "/search-chat",
    Dashboard = "/dashboard",
    TalentPool = "/talent-pool",
    FlowHistory = "/flow-history",
    Login = "/login",
    FlowPublish = "/flow-publish",
    JobManage = "/job-manage",
    ResumeDetail = "/resume-detail",
    Interviewer = "/interviewer",
    InterviewTime = "/interview-time",
    Candidate = "/candidate",
    CandidateAssign = "/candidate-assign",
    MyInterview = "/my-interview",
    JobDetail = "/job-detail",
    JobEdit = "/job-edit",
    EliminationPool = "/elimination-pool",
}

export enum ApiPath {
    Cors = "",
    Artifacts = "/api/artifacts",
    DeepSeek = "/api/deepseek",
}

export enum SlotID {
    AppBody = "app-body",
    CustomModel = "custom-model",
}

export enum FileName {
    Prompts = "prompts.json",
}

export enum StoreKey {
    Chat = "chat-next-web-store",
    Access = "access-control",
    Config = "app-config",
    Prompt = "prompt-store",
    Update = "chat-update",
    Sync = "sync",
}

export const DEFAULT_SIDEBAR_WIDTH = 220;
export const MAX_SIDEBAR_WIDTH = 600;
export const MIN_SIDEBAR_WIDTH = 50;
export const NARROW_SIDEBAR_WIDTH = 50;

// 使用屏幕宽度的百分比计算侧边栏宽度
export function getResponsiveSidebarWidth() {
    // 如果在客户端环境
    if (typeof window !== "undefined") {
        const screenWidth = window.innerWidth;

        // 使用屏幕宽度的百分比计算侧边栏宽度
        // 大屏幕用较小百分比，小屏幕用较大百分比，确保合理的宽度
        if (screenWidth >= 2560) {
            return Math.min(MAX_SIDEBAR_WIDTH, Math.max(MIN_SIDEBAR_WIDTH, Math.round(screenWidth * 0.12))); // 约12%屏幕宽度
        } else if (screenWidth >= 1920) {
            return Math.min(MAX_SIDEBAR_WIDTH, Math.max(MIN_SIDEBAR_WIDTH, Math.round(screenWidth * 0.14))); // 约14%屏幕宽度
        } else if (screenWidth >= 1440) {
            return Math.min(MAX_SIDEBAR_WIDTH, Math.max(MIN_SIDEBAR_WIDTH, Math.round(screenWidth * 0.16))); // 约16%屏幕宽度
        } else {
            return Math.min(MAX_SIDEBAR_WIDTH, Math.max(MIN_SIDEBAR_WIDTH, Math.round(screenWidth * 0.18))); // 约18%屏幕宽度
        }
    }

    // 服务器端渲染时返回默认值
    return DEFAULT_SIDEBAR_WIDTH;
}

export const ACCESS_CODE_PREFIX = "nk-";

export const LAST_INPUT_KEY = "last-input";
export const UNFINISHED_INPUT = (id: string) => "unfinished-input-" + id;

export const STORAGE_KEY = "chatgpt-next-web";

export const REQUEST_TIMEOUT_MS = 60000;
export const REQUEST_TIMEOUT_MS_FOR_THINKING = REQUEST_TIMEOUT_MS * 5;

export const EXPORT_MESSAGE_CLASS_NAME = "export-markdown";

export enum ServiceProvider {
    DeepSeek = "DeepSeek",
}

export enum ModelProvider {
    DeepSeek = "DeepSeek",
}

export const DeepSeek = {
    ExampleEndpoint: DEEPSEEK_BASE_URL,
    ChatPath: "chat/completions",
};

export const DEFAULT_INPUT_TEMPLATE = `{{input}}`; // input / time / model / lang
export const DEFAULT_SYSTEM_TEMPLATE = `
You are ChatGPT, a large language model trained by {{ServiceProvider}}.
Knowledge cutoff: {{cutoff}}
Current model: {{model}}
Current time: {{time}}
Latex inline: \\(x^2\\) 
Latex block: $$e=mc^2$$
`;

export const SUMMARIZE_MODEL = "deepseek-chat";
export const DEEPSEEK_SUMMARIZE_MODEL = "deepseek-chat";

export const KnowledgeCutOffDate: Record<string, string> = {
    default: "2024-07",
    "deepseek-chat": "2024-07",
    "deepseek-coder": "2024-07",
    "deepseek-reasoner": "2024-07",
};

export const DEFAULT_TTS_ENGINE = "OpenAI-TTS";
export const DEFAULT_TTS_ENGINES = ["OpenAI-TTS", "Edge-TTS"];
export const DEFAULT_TTS_MODEL = "tts-1";
export const DEFAULT_TTS_VOICE = "alloy";
export const DEFAULT_TTS_MODELS = ["tts-1", "tts-1-hd"];
export const DEFAULT_TTS_VOICES = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"];

export const VISION_MODEL_REGEXES = [/vision/, /vl/i];

export const EXCLUDE_VISION_MODEL_REGEXES: RegExp[] = [];

const deepseekModels = ["deepseek-chat", "deepseek-coder", "deepseek-reasoner"];

let seq = 1000; // 内置的模型序号生成器从1000开始
export const DEFAULT_MODELS = [
    ...deepseekModels.map((name) => ({
        name,
        available: true,
        sorted: seq++,
        provider: {
            id: "deepseek",
            providerName: "DeepSeek",
            providerType: "deepseek",
            sorted: 1,
        },
    })),
] as const;

// 聊天页面配置
export const CHAT_PAGE_SIZE = 15;
export const MAX_RENDER_MSG_COUNT = 45;

// some famous webdav endpoints
export const internalAllowedWebDavEndpoints = [
    "https://dav.jianguoyun.com/dav/",
    "https://dav.dropdav.com/",
    "https://dav.box.com/dav",
    "https://nanao.teracloud.jp/dav/",
    "https://bora.teracloud.jp/dav/",
    "https://webdav.4shared.com/",
    "https://dav.idrivesync.com",
    "https://webdav.yandex.com",
    "https://app.koofr.net/dav/Koofr",
];

export const DEFAULT_GA_ID = "G-89WN60ZK2E";

// SaaS 聊天 URL
export const SAAS_CHAT_URL = "https://nextchat.club";
export const SAAS_CHAT_UTM_URL = "https://nextchat.club?utm=github";

// 招聘类型
export const recruitmentType = [
    {
        label: "校园招聘",
        value: 1,
    },
    {
        label: "社会招聘",
        value: 2,
        disabled: true,
    },
];

// 候选人页面类型
export const candidateType = [
    {
        label: "筛选简历",
        value: 1,
    },
    {
        label: "分配简历",
        value: 2,
    },
];

// 面试类型
export enum SCHEDULE_TYPE {
    free = "A", // 空闲日程
    interview = "B", // 面试日程
}

// 岗位类型
export enum JOB_TYPE {
    jobSpec = "spec", // 小需求
    jobPublish = "publish", // 大需求
}

// 评价类型
export enum EVALUATE_TYPE {
    first = 1, // 简历初筛
    other = 2, // 其他
}

// 面试类型筛选类型
export enum FILTER_TYPE {
    ResumeScreen = "ResumeScreen", // 简历待筛选
    ResumeScreened = "ResumeScreened", // 简历已筛选
    All = "All", // 全部
    NotStarted = "NotStarted", // 未开始
    NotEvaluated = "NotEvaluated", // 未评价
    Evaluated = "Evaluated", // 已完成
}
// 面试类型
export const INTERVIEW_TYPE = [
    {
        key: "ResumeScreen",
        label: "简历待筛选",
    },
    {
        key: "ResumeScreened",
        label: "简历已筛选",
    },
    {
        key: "All",
        label: "全部面试",
    },
    {
        key: "NotStarted",
        label: "面试未开始",
    },
    {
        key: "NotEvaluated",
        label: "面试未评价",
    },
    {
        key: "Evaluated",
        label: "面试已完成",
    },
];

// 待办流程状态
export enum TODO_STATUS {
    process = 0, // 进行中
    done = 1, // 已完成
    close = 7, // 已关闭
}
