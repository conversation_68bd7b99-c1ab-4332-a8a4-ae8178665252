import { JOB_TYPE } from "@/app/constant";
import http from "@/app/request";
import {
    CandidateListReq,
    InitInterviewReq,
    InterviewerScheduleReq,
    ResumeChangeStatusReq,
    ResumeResultReq,
    SaveAssignmentsReq,
    InterviewTranscriptReq,
} from "@/app/store/modules/candidate";

/**
 *
 * @param {string} deptCode 部门编码
 * @returns 获取小职位列表
 */
export const getSubJobListApi = (deptCode: string) => {
    return http.get("jobSpec/list/jobSpec/deptCode", { deptCode });
};

/**
 *
 * @param {string} deptCode 部门编码
 * @returns 获取大职位列表
 */
export const getMainJobListApi = (deptCode: string, allocationMethod: number) => {
    return http.get("jobSpec/list/jobPublish/deptCode", { deptCode, allocationMethod });
};

/**
 *
 * @param params deptCode 部门编码 showMore 状态：0-进行中
 * @returns 获取面试官可访问的职位列表
 */
export const getSubJobListByInterviewerApi = (params: { deptCode: string; showMore: number }) => {
    return http.post("hrWorkbench/filter/jobSpec/list", params);
};

/**
 *
 * @param {number} jobId 职位id
 * @returns 获取职位对应面试流程
 */
export const getInterviewFlowApi = (jobId: number) => {
    return http.get("track/process/job/statistics", { jobId });
};

/**
 *
 * @param {CandidateListReq} params
 * @returns 查询简历数据
 */
export const getCandidateListByJobIdApi = (params: CandidateListReq) => {
    return http.post("hrWorkbench/filter/list", params);
};

/**
 *
 * @param {CandidateListReq} params
 * @returns 查询简历数据
 */
export const getParentCandidateListByJobIdApi = (params: CandidateListReq) => {
    return http.get("assignment/applicant/searchApplicant", params);
};

/**
 *
 * @param id 面试评价表模板id
 * @returns 面试评价表模版详情
 */
export const getInterviewEvaluationTemplateApi = (id: string) => {
    return http.get("evaluation/searchTemplate", { id });
};

/**
 *
 * @param params
 * @returns 保存简历结果
 */
export const saveResumeResultApi = (params: ResumeResultReq) => {
    return http.post("interviewEvaluation/evaluations", params);
};

/**
 *
 * @param params
 * @returns 批量保存简历结果
 */
export const saveBatchResumeResultApi = (params: ResumeResultReq) => {
    return http.post("interviewEvaluation/evaluations/batch", params);
};

/**
 *
 * @param params {applicantId: 简历ID; jobId: 职位ID}
 * @returns 更新简历状态到下一个状态
 */
export const candidateNextStageApi = (params: { applicantId: string; jobId: string }) => {
    return http.get("track/process/manage/nextStage", params);
};

/**
 *
 * @param params
 * @returns 创建面试日程
 */
export const addInterviewScheduleApi = (params: InitInterviewReq) => {
    return http.post("schedule/interview/create", params);
};

/**
 *
 * @param params
 * @returns 获取面试日程列表
 */
export const getInterviewScheduleListApi = (params: InterviewerScheduleReq) => {
    return http.post("schedule/fetch", params);
};

/**
 *
 * @param params
 * @returns 分配简历
 */
export const assignCandidateApi = (params: SaveAssignmentsReq) => {
    return http.post("assignment/saveAssignments", params);
};

/**
 *
 * @param params
 * @returns 获取简历详情数据
 */
export const getResumeDetailApi = (params: { jobId: string; applicantId: string; jobType: JOB_TYPE }) => {
    return http.get("hrWorkbench/profile/detail", params);
};

/**
 *
 * @param params
 * @returns 更新简历状态
 */
export const resumeChangeStatusApi = (params: ResumeChangeStatusReq) => {
    return http.get("track/process/manage/change", params);
};

/**
 *
 * @param jobId 职位id
 * @param type 类型 1-小需求 2-大需求
 * @returns 按照职位ID获取对应的筛选条件(学历，工作年限，技能标签等)
 */
export const getCandidateFilterListApi = (jobId: number, type: number) => {
    return http.get("hrWorkbench/condition/default", { jobId, type });
};

/**
 *
 * @param params { applicantId: 简历ID; stageId: 流程ID }
 * @returns 开始面试，更新简历状态
 */
export const startInterviewApi = (params: { applicantId: string; stageId: string }) => {
    return http.post("schedule/interview/start", params);
};

/**
 *
 * @param params {empIds: 面试官ID数组}
 * @returns 获取面试官列表
 */
export const getInterviewerListApi = (params: { empIds: string[] }) => {
    return http.post("schedule/interview/fetch/interviewers", params);
};

/**
 *
 * @param params
 * @returns 获取简化的简历列表，用于详情页直接切换上一页/下一页
 */
export const getSimpleCandidateListApi = (params: CandidateListReq) => {
    return http.post("hrWorkbench/filter/list/id", params);
};

/**
 *
 * @param params { applicantId: 简历ID; jobId: 职位ID; reportName: 报告名称 }
 * @returns 获取北森测评报告
 */
export const getBeisenEvaluationReportApi = (params: { applicantId: string; jobId: string; reportName: string }) => {
    return http.downloadFile("beisen/eva-report", "GET", undefined, params);
};

// 获取面试记录
export const getInterviewTranscriptApi = (params: InterviewTranscriptReq) => {
    return http.get("hrWorkbench/interview/records", params);
};

/**
 *
 * @param params { interviewId: 候选人ID; interviewStage: 面试阶段code }
 * @returns 获取AI面试题目列表
 */
export const getAIQuestionsListApi = (params: { interviewId: string; interviewStage: string }) => {
    return http.get("interview/getAiInterviewQuestionsList", params);
};
