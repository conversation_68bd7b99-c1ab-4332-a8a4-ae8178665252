.flex-col {
    display: flex;
    flex-direction: column;
}
.flex-row {
    display: flex;
    flex-direction: row;
}
.flex-wrap {
    flex-wrap: wrap;
}
.flex-center {
    justify-content: center;
    align-items: center;
}
.justify-between {
    justify-content: space-between;
}
.justify-around {
    justify-content: space-around;
}
.justify-start {
    justify-content: flex-start;
}
.justify-end {
    justify-content: flex-end;
}
.align-start {
    align-items: flex-start;
}
.align-end {
    align-items: flex-end;
}
.align-center {
    align-items: center;
}
.align-baseline {
    align-items: baseline;
}
.align-stretch {
    align-items: stretch;
}
.flex-grow {
    flex-grow: 1;
}
.flex-shrink {
    flex-shrink: 1;
}
.flex-basis {
    flex-basis: 0;
}
.flex-none {
    flex: none;
}
.flex-auto {
    flex: 1 1 auto;
}
.gap-4 {
    gap: 4px;
}
.gap-8 {
    gap: 8px;
}
.gap-16 {
    gap: 16px;
}
.weight-400 {
    font-weight: 400;
}
.weight-500 {
    font-weight: 500;
}
.weight-600 {
    font-weight: 600;
}
.size-12 {
    font-size: 12px;
}
.size-14 {
    font-size: 14px;
}
.size-16 {
    font-size: 16px;
}
.size-18 {
    font-size: 18px;
}
.size-20 {
    font-size: 20px;
}
.p-8 {
    padding: 8px;
}
.p-16 {
    padding: 16px;
}
.p-24 {
    padding: 24px;
}
.m-8 {
    margin: 8px;
}
.m-16 {
    margin: 16px;
}
.m-24 {
    margin: 24px;
}

.wh-full {
    width: 100%;
    height: 100%;
}
.w-full {
    width: 100%;
}
.h-full {
    height: 100%;
}
