import { create } from "zustand";
import { PaginationInfo } from "@/app/typing";
import { FormInstance } from "antd";
import { Department, FlowItemParams, FlowItemResp, TagsResp } from "@/app/store/modules/dashboard/jobFlow";
import { EVALUATE_TYPE, SCHEDULE_TYPE } from "@/app/constant";

export enum ItvResult {
    NotStarted = "NOT_STARTED", // 未开始
    NotEvaluated = "NOT_EVALUATED", // 未评价
    Completed = "COMPLETED", // 已完成
    Cancelled = "CANCELLED", // 已取消
}

export interface ScheduleInterviewExpose {
    showDialog: (currentStage: TrackStageStatistic, resumeList: CandidateListResp[], currentJob: JobResp) => void;
}

export interface CandidateAssignExpose {
    showDialog: () => void;
}

type EvaluateType = EVALUATE_TYPE;
export interface InterviewEvaluationExpose {
    showDrawer: (
        type: EvaluateType,
        templateId: string,
        applicantId: string | string[],
        stageId: string,
        jobId: string,
        initValue?: string
    ) => void;
}

export interface ChangeStatusExpose {
    showDrawer: (applicantId: string, jobId: string, stageName: string, currentStage: TrackStageStatistic) => void;
}
export interface AddJobExpose {
    showDrawer: (applicantId: string, stageName: string, originJobName: string) => void;
}

export interface getFileBlobReq {
    jobId: string;
    applicationId: string;
    isOrigin: boolean;
    isManualDistribution: boolean;
}

export interface JobResp extends FlowItemParams {
    formatStr?: string;
    allocationMethod: number;
    jobSpecs?: FlowItemParams[];
    count?: number;
}

export interface InterviewFlowResp {
    processId: string;
    processName: string;
    total: number;
    totalOut: number;
    trackStageStatistics: TrackStageStatistic[];
}

export interface TrackStageStatistic {
    stageId: string;
    stageName: string;
    stageCode: string;
    total: number;
    trackStateStatistics: TrackStateStatistic[];
    currentState?: TrackStateStatistic;
    reviewTemplate?: string;
    handlers?: string[];
}

export interface TrackStateStatistic {
    stateId: string;
    stateName: string;
    stateCode: string;
    total: number;
}

export interface InterviewDimension {
    dimensionId: string;
    weight: number;
    isConclusion: boolean;
    description: string;
    questions: InterviewQuestion[];
}

export interface InterviewQuestion {
    questionId: string;
    type: string;
    isRequired: boolean;
    description?: string;
    options?: { value: string; score: number }[];
    selectedOptions?: { optionDescription: string; score: number }[];
}

export interface InterviewQuestionExpose {
    init: (initValue?: string) => void;
    formInstance: FormInstance;
}

export interface CandidateListReq extends PaginationInfo {
    jobId: number;
    trackStageId?: string;
    trackStatusId?: string;
    educationLevelId?: string;
    workExperienceYears?: string;
    techTags?: string[];
    graduationStartDate?: string;
    graduationEndDate?: string;
    trackCurrentStatus?: number;
    keyword?: string;
    currentHandler: string | null;
    orderBy?: { field: string; direction: number }[];
}

export interface CandidateProfileResp {
    applicantId: string;
    name: string;
    gender: string;
    birth?: string;
    workExperienceYears: number;
    graduationDate: string;
    phone: string;
    email: string;
    tags: {
        hardRequirements: number[];
        capabilityFit: number[];
        potentialForecast: number[];
    };
    educationExperience: {
        startDate: string;
        endDate: string;
        school: string;
        degree: string;
    }[];
    educationLevelId: string;
    workExperience: {
        startDate: string;
        endDate: string;
        company: string;
        position: string;
    }[];
    applications: [];
    applicantStatus: string;
    competitions: [];
    createdAt: string;
    updatedAt: string;
    collectionId: string;
    archived: boolean;
}

export interface CandidateTagsResp extends TagsResp {
    isMatched?: boolean;
}
export interface CandidateAbilityLayerScoreTagsResp {
    tag: number;
    isMatched: boolean;
}

export interface AIAnalysisResp {
    matchScore: number;
    ranking: string;
    talentEstimate: string;
    projectExperienceCount: number;
    abilityLayerScore: {
        hardRequirements: CandidateAbilityLayerScoreTagsResp[];
        hardRequirementsScore: number;
        capabilityFit: CandidateAbilityLayerScoreTagsResp[];
        capabilityFitScore: number;
        potentialForecast: CandidateAbilityLayerScoreTagsResp[];
        potentialForecastScore: number;
        hardRequirementsTags?: CandidateTagsResp[];
        capabilityFitTags?: CandidateTagsResp[];
        potentialForecastTags?: CandidateTagsResp[];
    };
}

// 短信相关
interface SmsSendResult {
    candidateId: string;
    errMsg: string;
    content: string;
    gmtSend: string;
    gmtReceive: string;
    sendStatus: string;
}

export interface CandidateApplicationResp {
    applicantId: string;
    jobId: string;
    applicationDate: string;
    portraitAnalysis: AIAnalysisResp;
    portraitMatching: {
        aiEvaluation: string;
        simplifiedReview: string;
    };
    trackStageId: string;
    trackStatusId: string;
    trackCurrentHandler: string;
    trackCurrentStatus: number;
    remarks: string[];

    // 短信相关
    smsSendDate: string;
    smsSendResult: SmsSendResult;
}

export interface TrackLogResp {
    id: string;
    jobId: string;
    stageId: string;
    reason: string;
    applicantId: string;
    operationType: number;
    operationTime: string;
    operatorEntity: {
        avatar: string;
        email: string;
        empId: string;
        empName: string;
    };
}
export interface CandidateListResp {
    profile: CandidateProfileResp;
    application: CandidateApplicationResp;
    trackLogs: TrackLogResp[];
    jobName?: string;
    allTags?: CandidateTagsResp[];
    jobSpec?: {
        jobId: string;
        jobNameInner: string;
        jobNameOuter: string;
        orgName: string;
    };
    stage?: {
        code: string;
        name: string;
        id: string;
        jobId: string;
        jobName: string;
        state?: {
            code: string;
            name: string;
            id: string;
        };
    };
}

export interface ResumeResultReq {
    jobId: string; // 职位需求id
    templateId?: string; // 面试模版id
    applicantId?: string; // 简历id
    applicantIds?: string[];
    stageId: string; // 面试阶段id
    isPreliminary: boolean; // 是否是初筛
    evaluationContent: {
        dimensions: InterviewDimension[]; // 面试维度/题目结果
    };
    // 简历标签
    applicantTags?: [
        {
            tagId: string;
            description: string;
            layer: string;
            name: string;
            label: string;
        },
    ];
    // 面试官信息
    interviewerInfo: {
        interviewerId: string;
        avatarUrl: string;
        name: string;
    };
}

export interface InterviewerListReq {
    id: string;
    name: string;
    avatar: string;
    time: string;
}

export interface InterviewerResp {
    empId: string;
    name: string;
    avatar: string;
}

type ScheduleType = SCHEDULE_TYPE;
export interface InterviewerScheduleReq {
    specificId?: string;
    targetEmpId: string[];
    startTime: string;
    endTime: string;
    period: ScheduleType;
}

interface InterviewerScheduleItem {
    deleted: boolean;
    gmt_create: string;
    gmt_modified: string;
    id: string;
    itr_emp_id: string;
    feishu_id: string;
    gmt_start: string;
    gmt_end: string;
    period: string;
    released: boolean;
}

interface InterviewerUserInfo {
    opr_id: string;
    name: string;
    avatar: string;
    emp_id: string;
}
export interface InterviewerScheduleResp {
    [empId: string]: {
        items: InterviewerScheduleItem[];
        info: InterviewerUserInfo;
    };
}

export interface InitInterviewReq {
    // 候选人列表
    candidates: {
        // 简历ID
        resumeId: string;
        // 阶段ID
        stageId: string;
    }[];
    // 面试官列表
    interviewers: string[];
    // 是否视频面试
    isVideo: boolean;
    // 邮件模板
    emailTemplate?: string;
    // 手动分配设置
    manuallyAssignSettings?: {
        // 地点
        loc: string;
        // 时间范围
        timeScopes: {
            [key in string]: {
                // 开始时间
                start: string;
                // 结束时间
                end: string;
            };
        };
    };
    positionName: string;
    evalTempId: string;
}

export interface InterviewEvaluationTemplateResp {
    // 面试维度ID
    id: string;
    // 面试维度标题
    title: string;
    // 面试维度描述
    description?: string;
    // 面试维度类型
    type: number;
    // 面试维度状态
    status: number;
    // 面试维度标签
    tags?: string[];
    // 面试维度评价类型
    evaluationType: number;
    // 面试维度所属组织ID
    orgIds: string[];
    // 面试维度所属组织名称
    orgNames: string[];
    // 面试维度详情
    detail: InterviewDimension[];
}

export interface SaveAssignmentsReq {
    // 候选人ID
    applicantIds: string[];
    //大需求ID
    publishId: string;
    // 小需求ID
    specId: string;
}

export interface CandidateFilterResp {
    // 职位ID
    jobId: string;
    // 学历
    educationLevelIds: { key: string; value: string }[];
    // 工作年限
    workExperienceYears: { key: string; value: string }[];
    // 技能标签
    techTags: { key: string; value: string }[];
    // 排序字段
    sortFields: { key: string; value: string }[];
}

export interface ResumeChangeStatusReq {
    // 简历ID
    applicantId: string;
    // 职位ID
    jobId: string;
    // 流程ID
    stageId: string;
    // 流程下状态ID
    stateId: string;
    // 备注
    reason?: string;
}

export interface InterviewTimeResp {
    id: string;
    appLink?: string;
    meetingLink?: string;
    description: string;
    summary: string;
}

export interface InterviewTranscriptReq {
    jobId: string;
    applicantId: string;
    // jobType: string;
}

export interface InterviewTranscriptResp {
    itvStageName: string; // 当前是初试，复试，还是终试

    id: string; // UUID -> string
    itrEmpId: string; // 面试官员工ID (UUID -> string)
    itrEmpName: string; // 面试官姓名
    itrEmpEmail: string; // 面试官邮箱
    itvStageId: string; // 面试阶段ID (UUID -> string)

    summary: string; // 面试摘要
    description: string; // 面试描述
    gmtStart: string; // 开始时间 (LocalDateTime -> string)
    gmtEnd: string; // 结束时间 (LocalDateTime -> string)
    gmtCreate: string; //
    loc: string; // 面试地点

    video: boolean; // 是否视频面试 (Boolean -> boolean)
    attendees: string[]; // 参与者列表 (List<String> -> string[])
    evalTempId: string; // 评估模板ID (UUID -> string)
    evalTemp: string; // 评价时间
    itvRes: string; // 面试结果
    itvEval: string; // 评价内容
    itvResult: ItvResult; // 面试结果
    isClassified: boolean; // 是否已分类 (Boolean -> boolean)

    meetingLink?: string; // 会议链接 (可选)
    appLink?: string; // 应用链接 (可选)
    hookScheduleId?: string; // 关联日程ID (UUID -> string, 可选)

    assginerEmpId: string; // 分配人员工ID (UUID -> string)
    assginerEmpName: string; // 分配人姓名

    hookCandidate?: Record<string, string>; // 关联候选人信息 (可选)
}

export interface InterviewerJobResp {
    jobSpecs: JobResp;
    count: number;
}

export interface AIQuestionResp {
    question: string;
    answer: string;
}

export interface CandidateState {
    defaultTreeData: Department[];
    defaultFlatTreeData: Department[];
    statusList: TrackStageStatistic[];
    currentCandidateType: number; // 1: 筛选简历 2: 分配简历
    candidateList: CandidateListResp[];
    selectedCandidate: CandidateListResp[];
    allTags: any[];
    currentDept?: Department;
    currentStage?: TrackStageStatistic;
    currentState?: TrackStateStatistic;
    currentJob?: JobResp;
    currentPagination: PaginationInfo;
    interviewTranscript?: InterviewTranscriptResp[];
    setDefaultTreeData: (data: any[]) => void;
    setDefaultFlatTreeData: (data: any[]) => void;
    setStatusList: (data: TrackStageStatistic[]) => void;
    setCurrentStage: (data: TrackStageStatistic | undefined) => void;
    setCurrentState: (data: TrackStateStatistic | undefined) => void;
    setCandidateList: (data: CandidateListResp[]) => void;
    setSelectedCandidate: (data: CandidateListResp[]) => void;
    setCurrentDept: (data: Department | undefined) => void;
    setCurrentJob: (data: JobResp | undefined) => void;
    setAllTags: (data: any[]) => void;
    setCurrentCandidateType: (data: number) => void;
    setCurrentPagination: (data: PaginationInfo) => void;
    setInterviewTranscript: (data: InterviewTranscriptResp[]) => void;
}

const UseCandidateStore = create<CandidateState>((set) => {
    return {
        defaultTreeData: [],
        defaultFlatTreeData: [],
        statusList: [],
        currentCandidateType: 1,
        candidateList: [],
        selectedCandidate: [],
        currentDept: undefined,
        currentJob: undefined,
        currentStage: undefined,
        currentState: undefined,
        allTags: [],
        currentPagination: {
            pageNum: 0,
            pageSize: 20,
            total: 0,
        },
        interviewTranscript: undefined,
        setDefaultTreeData: (data: any) => set({ defaultTreeData: data }),
        setDefaultFlatTreeData: (data: any) => set({ defaultFlatTreeData: data }),
        setStatusList: (data: TrackStageStatistic[]) => set({ statusList: data }),
        setCandidateList: (data: CandidateListResp[]) => set({ candidateList: data }),
        setSelectedCandidate: (data: CandidateListResp[]) => set({ selectedCandidate: data }),
        setAllTags: (data: any) => set({ allTags: data }),
        setCurrentCandidateType: (data: number) => set({ currentCandidateType: data }),
        setCurrentStage: (data?: TrackStageStatistic) => set({ currentStage: data }),
        setCurrentState: (data?: TrackStateStatistic) => set({ currentState: data }),
        setCurrentDept: (data?: Department) => set({ currentDept: data }),
        setCurrentJob: (data?: JobResp) => set({ currentJob: data }),
        setCurrentPagination: (data: PaginationInfo) => set({ currentPagination: data }),
        setInterviewTranscript: (data: InterviewTranscriptResp[]) => set({ interviewTranscript: data }),
    };
});

export default UseCandidateStore;
