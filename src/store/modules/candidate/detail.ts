import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export interface CandidateDetailStore {
    jobId: string | null;
    applicantId: string | null;
    setJobId: (jobId: string | null) => void;
    setApplicantId: (applicantId: string | null) => void;
    clear: () => void;
}

const useCandidateDetailStore = create<CandidateDetailStore>()(
    persist(
        (set, get) => {
            return {
                jobId: null,
                applicantId: null,
                setJobId: (jobId) => {
                    set(() => ({ jobId }));
                },
                setApplicantId: (applicantId) => {
                    set(() => ({ applicantId }));
                },
                clear: () => {
                    get().setJobId(null);
                    get().setApplicantId(null);
                },
            };
        },
        {
            name: "candidate-detail-store",
            storage: createJSONStorage(() => localStorage),
        }
    )
);

export default useCandidateDetailStore;
