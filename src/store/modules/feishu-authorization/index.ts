import { create } from "zustand";
import http from "@/app/request";
import { RespParams } from "@/app/typing";

// 飞书是否授权的响应体
export interface FeishuAuthorizationResponse {
    calendar: boolean;
    authorized: boolean;
}

// 飞书授权链接响应体
export interface FeishuAuthorizationUrlResponse {
    authUrl: string;
}

export interface FeishuAuthorizationStore {
    id: string;
    // 查询飞书是否授权
    queryFeishuAuthorization: (data: any) => Promise<FeishuAuthorizationResponse>;
    // 发起飞书授权 - 返回授权链接
    feishuAuthorization: (data: any) => Promise<string>;
    // 获取飞书授权链接（不调用API，直接构造链接）
    getFeishuAuthorizationUrl: (data: { redirect: string }) => string;
    // 完成飞书授权
    completeFeishuAuthorization: (data?: { code: string; redirect: string }) => Promise<boolean>;
}

const useFeishuAuthorizationStore = create<FeishuAuthorizationStore>((set) => ({
    id: "", // id 的初始状态
    queryFeishuAuthorization: async (data) => {
        try {
            const response = await http.get("schedule/authorization/feishu/calendar/check", data);
            const result = (await response.json()) as RespParams<FeishuAuthorizationResponse>;
            return result.data;
        } catch (error) {
            console.error("查询飞书是否授权报错:", error);
            throw error;
        }
    },
    feishuAuthorization: async (data) => {
        try {
            const response = await http.get("schedule/authorization/feishu/calendar/auth", data);
            const result = (await response.json()) as RespParams<string>;
            return result.data;
        } catch (error) {
            console.error("发起飞书授权报错:", error);
            throw error;
        }
    },
    getFeishuAuthorizationUrl: (data: { redirect: string }) => {
        // 构造飞书授权链接
        const appId = "cli_a8cab5fedc1ed013";
        const redirectUri = encodeURIComponent(data.redirect);
        const state = "STATE";
        const scope = "offline_access";

        return `https://open.feishu.cn/open-apis/authen/v1/index?app_id=${appId}&redirect_uri=${redirectUri}&state=${state}&scope=${scope}`;
    },
    completeFeishuAuthorization: async (data?: { code: string }) => {
        try {
            const response = data
                ? await http.get("schedule/authorization/feishu/calendar/bind", data)
                : await http.get("schedule/authorization/feishu/calendar/bind");
            const result = (await response.json()) as RespParams<boolean>;
            return result.data;
        } catch (error) {
            console.error("完成飞书授权报错:", error);
            throw error;
        }
    },
}));

export default useFeishuAuthorizationStore;
