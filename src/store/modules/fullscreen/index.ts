import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export interface FullScreenState {
    isFullScreen: boolean;
    fullScreenContainerId: string;
    setIsFullScreen: (isFullScreen: boolean) => void;
    toggleFullScreen: (containerRef: HTMLElement | null, fullScreenContainerId: string) => void;
}

const useFullScreenStore = create<FullScreenState>()(
    persist(
        (set, get) => {
            return {
                isFullScreen: false,
                fullScreenContainerId: "",
                toggleFullScreen: async (containerRef, fullScreenContainerId) => {
                    if (!containerRef) return;
                    let fullscreen = false;
                    try {
                        if (!document.fullscreenElement) {
                            // 进入全屏
                            await containerRef.requestFullscreen();
                            fullscreen = true;
                        } else {
                            // 退出全屏
                            await document.exitFullscreen();
                            fullscreen = false;
                        }
                    } catch (error) {
                        console.error("全屏操作失败:", error);
                    }
                    set(() => ({ isFullScreen: fullscreen, fullScreenContainerId }));
                },
                setIsFullScreen: (isFullScreen) => {
                    set(() => ({ isFullScreen }));
                },
            };
        },
        {
            name: "fullscreen-store",
            storage: createJSONStorage(() => localStorage),
        }
    )
);

export default useFullScreenStore;
