// 这个文件是用于在左侧侧边栏完全收起的时候，将什么的内容作为title的一部分展示在哪里，主要还是用于对话时的头部按钮板块
import { create } from "zustand";

export interface TitleStore {
    titleComponents: Record<string, JSX.Element>;
    addTitleComponent: (key: string, component: JSX.Element) => void;
    removeTitleComponent: (key: string) => void;
    clearTitleComponents: () => void;
}

export const useTitleStore = create<TitleStore>()((set) => ({
    titleComponents: {},

    addTitleComponent: (key: string, component: JSX.Element) =>
        set((state) => ({
            titleComponents: {
                ...state.titleComponents,
                [key]: component,
            },
        })),

    removeTitleComponent: (key: string) =>
        set((state) => {
            const newComponents = { ...state.titleComponents };
            delete newComponents[key];
            return {
                titleComponents: newComponents,
            };
        }),

    clearTitleComponents: () => set({ titleComponents: {} }),
}));
