import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export interface UserResp {
    avatar: string;
    emp_id: string;
    emp_name: string;
    gender: string;
    perms: string[];
    permGroups: string[];
}
export interface UserInfoResp {
    token: string;
    user: UserResp;
}

interface IUserInfo {
    token: string;
    state: string;
    logging: boolean;
    user: UserResp;
}

export interface UserInfoState extends IUserInfo {
    getValue: () => IUserInfo;
    setToken: (token: string) => void;
    setState: (state: string) => void;
    setLogging: (logging: boolean) => void;
    setUser: (user: UserResp) => void;
}

const useUserInfoStore = create<UserInfoState>()(
    persist(
        (set, get) => {
            return {
                token: "",
                state: "",
                logging: false,
                user: {
                    avatar: "",
                    emp_id: "",
                    emp_name: "",
                    gender: "",
                    perms: [],
                    permGroups: [],
                },
                getValue: () => get(),
                setToken: (token: string) => set(() => ({ token })),
                setState: (state: string) => set(() => ({ state })),
                setLogging: (logging: boolean) => set(() => ({ logging })),
                setUser: (user: UserResp) => set(() => ({ user })),
            };
        },
        {
            name: "chat-userinfo-store", // localStorage 中的键名
            storage: createJSONStorage(() => localStorage), // 使用 localStorage 作为存储
        }
    )
);

export default useUserInfoStore;
