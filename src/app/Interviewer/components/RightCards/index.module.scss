.container-right {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .container-top {
        width: 100%;
        height: 60.2%;
        border-radius: 12px;
        background: rgba(255, 255, 255, 1);
        padding: 20px;
        box-sizing: border-box;
    }

    .container-bottom {
        width: 100%;
        flex: 1;
        border-radius: 12px;
        background: rgba(255, 255, 255, 1);
        padding: 20px;
        box-sizing: border-box;
        min-height: 0;
    }
}

.pending-resume-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;

    .pending-resume-card-title {
        color: rgba(0, 0, 0, 0.86);
        font-family: "PingFang SC";
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        margin-bottom: 16px;
    }

    .resume-card-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        min-height: 0;

        .resume-card-content-item {
        }
    }
}

.resume-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow-y: auto;
    padding-right: 8px;

    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.04);
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 3px;

        &:hover {
            background: rgba(0, 0, 0, 0.3);
        }
    }
}

.card-item-container {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 0 0 12px 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    cursor: pointer;
}

.item-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
}

.grid-row-position {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    color: rgba(0, 0, 0, 0.6);
    font-family: "PingFang SC";
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
}

.grid-row-info {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    color: rgba(0, 0, 0, 0.86);
    font-family: "PingFang SC";
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
}

.evaluate-wrapper {
    display: flex;
    align-items: center;
    gap: 4px;
    .evaluate-grade {
        // font-size: 16px;
    }
}

.rate-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    .rate {
        font-size: 16px;
    }
}

.interview-detail-card {
    display: flex;
    flex-direction: column;
    height: 100%;

    .interview-detail-card-title {
        color: #000000db;
        font-family: "PingFang SC";
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
    }

    .interview-detail-card-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
        overflow: hidden;
    }
}

.interview-detail-item {
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    padding: 0 0 12px 12px;

    .interview-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
        flex: 1;

        .grow-row-interview-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            color: rgba(0, 0, 0, 0.86);
            font-family: "PingFang SC";
            font-weight: 400;
            font-size: 16px;
            line-height: 22px;

            .interview-info-title {
                color: rgba(0, 0, 0, 0.6);
                font-size: 14px;
                line-height: 20px;
            }
        }

        .grow-row-interview-detail {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            align-items: center;
            color: rgba(0, 0, 0, 0.6);
            font-family: "PingFang SC";
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }
    }
}

.interview-detail-content {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .view-all-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 12px 0;
        border-top: 1px solid rgba(0, 0, 0, 0.08);
        margin-top: 8px;

        .view-all-btn {
            color: #0099F2 !important;
            font-family: "PingFang SC";
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            padding: 4px 8px;
            height: auto;

            &:hover {
                color: #007ACC !important;
            }

            .anticon {
                font-size: 12px;
                margin-left: 4px;
            }
        }
    }
}

// 为面试详情的 Tabs 组件设置样式
.interview-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    :global {
        .ant-tabs-nav {
            flex-shrink: 0;
            margin-bottom: 16px;
        }

        .ant-tabs-tab-active {
            .ant-tabs-tab-btn {
                color: rgba(0, 153, 242, 1) !important;
                text-shadow: 0 0 0.25px rgba(0, 153, 242, 1) !important;
            }
        }

        .ant-tabs-content-holder {
            flex: 1;
            overflow: hidden;

            .ant-tabs-content {
                height: 100%;

                .ant-tabs-tabpane {
                    height: 100%;
                    overflow-y: auto;
                    padding-right: 8px;

                    &::-webkit-scrollbar {
                        width: 6px;
                    }

                    &::-webkit-scrollbar-track {
                        background: rgba(0, 0, 0, 0.04);
                        border-radius: 3px;
                    }

                    &::-webkit-scrollbar-thumb {
                        background: rgba(0, 0, 0, 0.2);
                        border-radius: 3px;

                        &:hover {
                            background: rgba(0, 0, 0, 0.3);
                        }
                    }
                }
            }
        }
    }
}
