import styles from "./index.module.scss";
import { Tag, Rate, Button, Tabs, type TabsProps, Skeleton, Empty, Flex, Popover, Checkbox, Avatar } from "antd";
import { CloseOutlined, RightOutlined } from "@ant-design/icons";
import { EVALUATE_TYPE, Path } from "@/app/constant";
import { useNavigate } from "react-router-dom";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import UseCandidateStore, { CandidateListResp, InterviewEvaluationExpose } from "@/app/store/modules/candidate";
import { getAllFiltersResumeListApi } from "@/app/request/modules/interview";
import { RespPaginationParams } from "@/app/typing";
import { getAllTagsApi } from "@/app/request/modules/dashboard";
import Tags from "@/app/components/Tags";
import { TextView } from "@/app/components/TextView";
import ProfileFit from "@/app/components/ProfileFit";
import InterviewEvaluation from "@/app/views/Candidate/InterviewEvaluation";
import BottomMultiple from "@/app/components/BottomMultiple";
import type { InterviewerItem } from "@/app/store/modules/interviewer";
import { InterviewStatus } from "@/app/store/modules/interview-time";
import useCandidateDetailStore from "@/app/store/modules/candidate/detail";
import { FILTER_TYPE } from "@/app/constant";

const ResumeCardItem = ({
    item,
    isSelect = false,
    handleCardSelect,
    handleAccept,
    handleReject,
}: {
    item: CandidateListResp;
    isSelect?: boolean;
    handleCardSelect?: (talent: CandidateListResp) => void;
    handleAccept: (item: CandidateListResp) => void;
    handleReject: (item: CandidateListResp) => void;
}) => {
    const navigate = useNavigate();
    const clear = useCandidateDetailStore((state) => state.clear);

    const [showPopover, setShowPopover] = useState(false);
    const [checkedSelected, setCheckedSelected] = useState(isSelect);

    const jobNameStr = useMemo(() => {
        const nameList = item.jobSpec?.jobNameInner?.split("/") ?? [];
        return `${nameList?.[nameList?.length - 1]}-${item.jobSpec?.jobNameOuter}`;
    }, [item.jobSpec?.jobNameInner, item.jobSpec?.jobNameOuter]);
    const orgNameStr = useMemo(() => {
        const nameList = item.jobSpec?.orgName?.split("/") ?? [];
        nameList.splice(0, 3);
        return nameList.join("/");
    }, [item.jobSpec?.orgName]);

    useEffect(() => {
        setCheckedSelected(isSelect);
    }, [isSelect]);

    // 跳转简历详情
    const handleResumeDetail = (item: CandidateListResp) => {
        clear();
        navigate(Path.ResumeDetail, {
            state: {
                jobId: item.application.jobId,
                jobName: jobNameStr?.split("-")?.[0] ?? "",
                applicantId: item.application.applicantId,
                mode: "Interviewer",
                isManualDistribution: false,
            },
        });
    };

    const onSelectChange = () => {
        setCheckedSelected(!checkedSelected);
        handleCardSelect?.(item);
    };

    return (
        <div className={styles["card-item-container"]} onClick={onSelectChange}>
            <Checkbox checked={checkedSelected} onChange={onSelectChange} />
            <div className={styles["item-content"]}>
                <div className={styles["grid-row-info"]}>
                    <div>{item.profile.name}</div>
                    <div>
                        <TextView text={jobNameStr ?? "暂无"} />
                    </div>
                    <div>
                        <TextView text={item.profile.educationExperience?.[0]?.school ?? "暂无"} />
                    </div>
                    <div className={styles["evaluate-wrapper"]}>
                        {/* <Tag color="#2ebf47" className={styles["evaluate-grade"]}>
                            <CheckCircleOutlined style={{ marginRight: 4 }} />
                            符合
                        </Tag> */}
                        {item.application.portraitAnalysis.matchScore}分
                    </div>
                </div>

                <div className={styles["grid-row-position"]}>
                    <div className={styles["grid-block"]}>
                        <Tags
                            tags={
                                item.allTags?.map((item) => ({
                                    name: item.name,
                                    isMatched: item.isMatched ?? true,
                                })) ?? []
                            }
                            color="#465ce8"
                        />
                    </div>
                    <div className={styles["grid-block"]}>
                        <TextView text={orgNameStr ?? "暂无"} />
                    </div>
                    <div className={styles["grid-block"]}>
                        <TextView text={item.profile.educationExperience?.[0]?.degree ?? "暂无"} />
                    </div>
                    <div className={styles["rate-wrapper"]}>
                        <Popover
                            trigger="click"
                            open={showPopover}
                            getPopupContainer={() => document.body}
                            onOpenChange={(open: boolean) => {
                                setShowPopover(open);
                            }}
                            content={
                                <ProfileFit
                                    detail={item?.application.portraitAnalysis}
                                    aiAnalysisStr={item?.application.portraitMatching.aiEvaluation}
                                />
                            }
                            title={() => (
                                <Flex justify="space-between" align="center">
                                    <span>画像匹配度</span>
                                    <CloseOutlined onClick={() => setShowPopover(false)} />
                                </Flex>
                            )}
                        >
                            <div onClick={() => setShowPopover(true)}>
                                <Rate
                                    value={item.application.portraitAnalysis.matchScore}
                                    disabled
                                    allowHalf
                                    className={styles["rate"]}
                                />
                                {item.application.portraitAnalysis.matchScore}
                            </div>
                        </Popover>
                    </div>
                </div>
            </div>
            <Flex gap={12}>
                <Button
                    variant="outlined"
                    color="green"
                    className={styles["detail-btn"]}
                    onClick={(e) => {
                        e.stopPropagation();
                        handleAccept(item);
                    }}
                >
                    通过
                </Button>
                <Button
                    variant="outlined"
                    color="danger"
                    className={styles["detail-btn"]}
                    onClick={(e) => {
                        e.stopPropagation();
                        handleReject(item);
                    }}
                >
                    淘汰
                </Button>
                <Button type="default" className={styles["detail-btn"]} onClick={() => handleResumeDetail(item)}>
                    查看详情
                </Button>
            </Flex>
        </div>
    );
};

const PendingResumeCard = () => {
    const navigate = useNavigate();

    const [loading, setLoading] = useState<boolean>(false);
    const [showAll, setShowAll] = useState<boolean>(false);
    const [resumeList, setResumeList] = useState<CandidateListResp[]>([]);
    const [selectResumeList, setSelectResumeList] = useState<CandidateListResp[]>([]);
    const { allTags, setAllTags } = UseCandidateStore((state) => ({
        allTags: state.allTags,
        setAllTags: state.setAllTags,
    }));
    // 底部多选框全部选中
    const checkAll = useMemo(() => {
        if (resumeList.length === 0) return false;
        return resumeList.every((candidate) =>
            selectResumeList.find((selected) => selected.profile.applicantId === candidate.profile.applicantId)
        );
    }, [selectResumeList, resumeList]);
    // 底部多选框部分选中
    const indeterminate = useMemo(() => {
        const filterList = resumeList.filter((candidate) =>
            selectResumeList.find((selected) => selected.profile.applicantId === candidate.profile.applicantId)
        );

        if (filterList.length === 0) return false;

        return filterList.length < resumeList.length;
    }, [selectResumeList, resumeList]);

    const interviewEvaluationRef = useRef<InterviewEvaluationExpose>(null);

    const getAllTags = async () => {
        const res: any = await (await getAllTagsApi({ label: "", layer: "" })).json();
        if (res.code === 200) {
            setAllTags(res.data);
        }
    };

    const getAllResumeList = useCallback(async () => {
        if (allTags.length === 0) return;
        try {
            setLoading(true);
            const res: RespPaginationParams<CandidateListResp> = await (await getAllFiltersResumeListApi(5)).json();
            if (res.code === 200) {
                setResumeList(
                    res?.data?.records?.map((item) => {
                        const hardRequirements = item.application.portraitAnalysis.abilityLayerScore.hardRequirements
                            .slice(0, 5)
                            .map((item) => {
                                const currentTags = allTags.find((tag) => tag.id === item.tag);
                                return { ...currentTags, isMatched: item.isMatched };
                            });
                        const capabilityFit = item.application.portraitAnalysis.abilityLayerScore.capabilityFit
                            .slice(0, 5)
                            .map((item) => {
                                const currentTags = allTags.find((tag) => tag.id === item.tag);
                                return { ...currentTags, isMatched: item.isMatched };
                            });
                        const potentialForecast = item.application.portraitAnalysis.abilityLayerScore.potentialForecast
                            .slice(0, 5)
                            .map((item) => {
                                const currentTags = allTags.find((tag) => tag.id === item.tag);
                                return { ...currentTags, isMatched: item.isMatched };
                            });

                        item.allTags = [...hardRequirements, ...capabilityFit, ...potentialForecast].filter(
                            (item) => item.isMatched
                        );

                        item.application.portraitAnalysis.abilityLayerScore.hardRequirementsTags = hardRequirements;
                        item.application.portraitAnalysis.abilityLayerScore.capabilityFitTags = capabilityFit;
                        item.application.portraitAnalysis.abilityLayerScore.potentialForecastTags = potentialForecast;
                        return item;
                    }) ?? []
                );
                if (res.data.total > 5) {
                    setShowAll(true);
                } else {
                    setShowAll(false);
                }
                setLoading(false);
            }
        } catch (err) {
            setResumeList([]);
            setLoading(false);
            setShowAll(false);
        }
    }, [allTags]);

    useEffect(() => {
        getAllTags();
    }, []);

    useEffect(() => {
        getAllResumeList();
    }, [getAllResumeList]);

    return (
        <div className={styles["pending-resume-card"]}>
            <div className={styles["pending-resume-card-title"]}>待筛选简历</div>
            <div className={styles["resume-card-content"]}>
                {loading ? (
                    <Skeleton paragraph={{ rows: 10 }} active />
                ) : (
                    <div className={styles["resume-list"]}>
                        {resumeList.length > 0 ? (
                            resumeList.map((item) => (
                                <ResumeCardItem
                                    key={item.profile.applicantId}
                                    item={item}
                                    isSelect={
                                        selectResumeList.find(
                                            (item2) => item.profile.applicantId === item2.profile.applicantId
                                        )
                                            ? true
                                            : false
                                    }
                                    handleCardSelect={(item) => {
                                        const exists = selectResumeList.find(
                                            (item2) => item.profile.applicantId === item2.profile.applicantId
                                        );
                                        if (exists) {
                                            setSelectResumeList(
                                                selectResumeList.filter(
                                                    (item2) => item.profile.applicantId !== item2.profile.applicantId
                                                )
                                            );
                                        } else {
                                            setSelectResumeList([...selectResumeList, item]);
                                        }
                                    }}
                                    handleAccept={(item) => {
                                        interviewEvaluationRef.current?.showDrawer(
                                            EVALUATE_TYPE.first,
                                            "",
                                            item.application.applicantId,
                                            item.application.trackStageId ?? "",
                                            item.application.jobId,
                                            "通过"
                                        );
                                    }}
                                    handleReject={(item) => {
                                        interviewEvaluationRef.current?.showDrawer(
                                            EVALUATE_TYPE.first,
                                            "",
                                            item.application.applicantId,
                                            item.application.trackStageId ?? "",
                                            item.application.jobId,
                                            "淘汰"
                                        );
                                    }}
                                />
                            ))
                        ) : (
                            <Flex style={{ marginTop: "8%" }} justify="center" align="center">
                                <Empty />
                            </Flex>
                        )}
                    </div>
                )}

                <Flex style={{ borderTop: "1px solid var(--border-color)", paddingTop: 12 }} align="center">
                    <Checkbox
                        indeterminate={indeterminate}
                        onChange={() => {
                            if (checkAll) {
                                // 当前状态为全选，点击后取消当前页选中数据
                                setSelectResumeList(
                                    selectResumeList.filter(
                                        (item) =>
                                            !resumeList.find(
                                                (item2) => item2.profile.applicantId === item.profile.applicantId
                                            )
                                    )
                                );
                            } else {
                                // 点击后选中当前页所有数据，如果当前页数据与选中数据有重复，则去重
                                setSelectResumeList(Array.from(new Set([...selectResumeList, ...resumeList])));
                            }
                        }}
                        checked={checkAll}
                    >
                        全选
                    </Checkbox>
                    <Flex style={{ flex: 1 }} justify="center" align="center">
                        <Button
                            type="link"
                            className={styles["view-all-btn"]}
                            icon={<RightOutlined />}
                            iconPosition="end"
                            style={{ color: "#0099F2" }}
                            onClick={() => {
                                navigate(Path.MyInterview);
                            }}
                        >
                            查看全部
                        </Button>
                    </Flex>
                </Flex>
            </div>
            <InterviewEvaluation
                ref={interviewEvaluationRef}
                refresh={() => {
                    getAllResumeList();
                    setSelectResumeList([]);
                }}
            />
            <div style={{ fontSize: 14 }}>
                <BottomMultiple selectedNum={selectResumeList.length} handleClose={() => setSelectResumeList([])}>
                    <Flex gap={12}>
                        <span
                            style={{ cursor: "pointer" }}
                            onClick={() => {
                                interviewEvaluationRef.current?.showDrawer(
                                    EVALUATE_TYPE.first,
                                    "",
                                    selectResumeList.map((item) => item.profile.applicantId),
                                    selectResumeList?.[0]?.application?.trackStageId ?? "",
                                    selectResumeList?.[0]?.application?.jobId,
                                    "淘汰"
                                );
                            }}
                        >
                            批量淘汰
                        </span>
                        <span
                            style={{ cursor: "pointer" }}
                            onClick={() => {
                                interviewEvaluationRef.current?.showDrawer(
                                    EVALUATE_TYPE.first,
                                    "",
                                    selectResumeList.map((item) => item.profile.applicantId),
                                    selectResumeList?.[0]?.application?.trackStageId ?? "",
                                    selectResumeList?.[0]?.application?.jobId,
                                    "通过"
                                );
                            }}
                        >
                            批量通过
                        </span>
                    </Flex>
                </BottomMultiple>
            </div>
        </div>
    );
};

const InerviewDatailItem = ({ item }: { item: InterviewerItem }) => {
    const navigate = useNavigate();
    const clear = useCandidateDetailStore((state) => state.clear);

    const handleViewDetail = () => {
        clear();
        navigate(Path.ResumeDetail, {
            state: {
                jobId: item.jobId,
                jobName: item.jobName,
                applicantId: item.candidateId,
                mode: "Interviewer",
                isManualDistribution: false,
            },
        });
    };

    return (
        <div className={styles["interview-detail-item"]}>
            <div className={styles["interview-content"]}>
                <div className={styles["grow-row-interview-info"]}>
                    <div>{item.title}</div>
                    <div className={styles["interview-info-title"]}>
                        面试信息：
                        <Tag bordered={false} color="rgba(0, 0, 0, 0.04)">
                            <span style={{ color: "rgba(0, 0, 0, 0.45)", lineHeight: "22px" }}>
                                {item.metadata.stageName}
                            </span>
                        </Tag>
                        <Tag bordered={false} color="#d9f0fd">
                            <span style={{ color: "rgba(0, 153, 242, 1)", lineHeight: "22px" }}>
                                {item.metadata.isVideo ? "视频面试" : "现场面试"}
                            </span>
                        </Tag>
                    </div>
                </div>
                <div className={styles["grow-row-interview-detail"]}>
                    <div>
                        面试时间：{item.gmtStart} ~ {item.gmtEnd}
                    </div>
                    <div>
                        面试联系人： <Avatar src={item.assigner.avatar} style={{ marginRight: 8 }} />
                        {item.assigner.name}-{item.assigner.empId}
                    </div>
                </div>
            </div>
            <Button className={styles["grow-row-btn"]} onClick={handleViewDetail}>
                查看详情
            </Button>
        </div>
    );
};

const InterviewDetailContent = ({ data, tabType }: { data: InterviewerItem[]; tabType?: string }) => {
    const navigate = useNavigate();
    const maxDisplayItems = 5;
    const shouldShowViewAll = data.length > maxDisplayItems;
    const displayData = shouldShowViewAll ? data.slice(0, maxDisplayItems) : data;

    const handleViewAll = () => {
        // 根据FILTER_TYPE映射到MyInterview页面对应的tab
        let targetTab = FILTER_TYPE.All; // 默认跳转到"全部面试"tab

        if (tabType === FILTER_TYPE.NotStarted) {
            targetTab = FILTER_TYPE.NotStarted; // "面试未开始"tab
        } else if (tabType === FILTER_TYPE.NotEvaluated) {
            targetTab = FILTER_TYPE.NotEvaluated; // "面试未评价"tab
        } else if (tabType === FILTER_TYPE.Evaluated) {
            targetTab = FILTER_TYPE.Evaluated; // "面试已完成"tab
        }

        navigate(Path.MyInterview, {
            state: {
                tab: targetTab,
            },
        });
    };

    return (
        <div className={styles["interview-detail-content"]}>
            {data.length > 0 ? (
                <>
                    {displayData.map((item) => (
                        <InerviewDatailItem key={`${item.jobId}-${item.candidateId}-${item.gmtStart}`} item={item} />
                    ))}
                    {shouldShowViewAll && (
                        <div className={styles["view-all-container"]}>
                            <Button
                                type="link"
                                className={styles["view-all-btn"]}
                                icon={<RightOutlined />}
                                iconPosition="end"
                                onClick={handleViewAll}
                            >
                                查看全部
                            </Button>
                        </div>
                    )}
                </>
            ) : (
                <Flex style={{ marginTop: "2%" }} justify="center" align="center">
                    <Empty description="暂无数据" />
                </Flex>
            )}
        </div>
    );
};

const InterviewDetailCard = ({ tableData }: { tableData: InterviewerItem[] }) => {
    // 根据面试状态分类数据
    const notStartData = useMemo(
        () => tableData.filter((item) => item.metadata.interviewState === InterviewStatus.NotStart),
        [tableData]
    );

    const notResultData = useMemo(
        () => tableData.filter((item) => item.metadata.interviewState === InterviewStatus.NotResult),
        [tableData]
    );

    const completedData = useMemo(
        () => tableData.filter((item) => item.metadata.interviewState === InterviewStatus.Completed),
        [tableData]
    );

    const tabItems: TabsProps["items"] = [
        {
            key: "1",
            label: `未开始 (${notStartData.length})`,
            children: <InterviewDetailContent data={notStartData} tabType={FILTER_TYPE.NotStarted} />,
        },
        {
            key: "2",
            label: `未评价 (${notResultData.length})`,
            children: <InterviewDetailContent data={notResultData} tabType={FILTER_TYPE.NotEvaluated} />,
        },
        {
            key: "3",
            label: `已完成 (${completedData.length})`,
            children: <InterviewDetailContent data={completedData} tabType={FILTER_TYPE.Evaluated} />,
        },
    ];

    return (
        <div className={styles["interview-detail-card"]}>
            <div className={styles["interview-detail-card-title"]}>面试详情</div>
            <div className={styles["interview-detail-card-content"]}>
                <Tabs defaultActiveKey="1" items={tabItems} className={styles["interview-tabs"]} />
            </div>
        </div>
    );
};

export default function RightCards({ tableData }: { tableData: InterviewerItem[] }) {
    return (
        <div className={styles["container-right"]}>
            <div className={styles["container-top"]}>
                <PendingResumeCard />
            </div>
            <div className={styles["container-bottom"]}>
                <InterviewDetailCard tableData={tableData} />
            </div>
        </div>
    );
}
