import styles from "./index.module.scss";
import React, { useEffect, useState } from "react";
import LeftCards from "./components/LeftCards";
import RightCards from "./components/RightCards";
import useInterviewerStore, { InterviewerItem, AllInterview, StatisticItem } from "@/app/store/modules/interviewer";
import dayjs from "dayjs";

export default function Interviewer() {
    const [leftTableData, setLeftTableData] = useState<InterviewerItem[]>([]); // LeftCards的数据（按日期筛选）
    const [countRecord, setCountRecord] = useState<StatisticItem>();
    const [rightTableData, setRightTableData] = useState<InterviewerItem[]>([]); // RightCards的数据（全部数据）
    const [dateRange, setDateRange] = useState<{ gmtStart?: string; gmtEnd?: string }>({});
    const { getInterviewerList } = useInterviewerStore();

    // 获取全部数据（用于RightCards）
    const fetchAllData = async () => {
        const res = await getInterviewerList({});
        setRightTableData(res.interviews);
    };

    // 获取指定日期范围的数据（用于LeftCards）
    const fetchDateData = async (params: { gmtStart?: string; gmtEnd?: string }) => {
        const res = (await getInterviewerList(params)) as AllInterview;
        setLeftTableData(res.interviews);
        setCountRecord(res.statistic);
    };

    const handleDateChange = (gmtStart?: string, gmtEnd?: string) => {
        setDateRange({ gmtStart, gmtEnd });
        fetchDateData({ gmtStart, gmtEnd });
    };

    useEffect(() => {
        // 初始化时获取全部数据和今天的数据
        fetchAllData();

        const today = dayjs();
        const startOfDay = today.startOf("day");
        const endOfDay = today.endOf("day");

        handleDateChange(startOfDay.format("YYYY-MM-DD HH:mm:ss"), endOfDay.format("YYYY-MM-DD HH:mm:ss"));
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <div className={styles["interviewer-container"]}>
            {/* 左侧 */}
            <LeftCards onDateChange={handleDateChange} tableDate={leftTableData} countRecord={countRecord} />
            {/* 右侧 */}
            <RightCards tableData={rightTableData} />
        </div>
    );
}
