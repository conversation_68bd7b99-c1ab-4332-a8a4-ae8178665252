import styles from "./index.module.scss";
import { useNavigate } from "react-router-dom";
import { useAppConfig, useChatStore, SubmitKey } from "../../store";
import { AttachedFile, ChatInput } from "../../components/ChatInput";
import { useDebouncedCallback } from "use-debounce";
import React, { useRef, useState, useEffect, useCallback } from "react";
// import { Path } from "../../constant";
import Locale from "../../locales";
import { autoGrowTextArea, useMobileScreen, isVisionModel } from "../../utils";
import { uploadImage as uploadImageRemote } from "@/app/utils/chat";
// import { isEmpty } from "lodash-es";
import { formatFileSize } from "../../utils/format";
// import { createMessage } from "../../store";
import { useChatCommand } from "../../command";
import { message } from "antd";
import { TitleContent } from "./title-content";
import { CardContent } from "./card-content";
// 从 ChatInput 接收的 props 接口定义
interface ChatInputProps {
    inputRef: React.RefObject<HTMLTextAreaElement>;
    userInput: string;
    onUserInput: (text: string) => void;
    onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
    onPaste: (event: React.ClipboardEvent<HTMLTextAreaElement>) => void;
    onSubmit: () => void;
    attachImages: AttachedFile[];
    setAttachImages: React.Dispatch<React.SetStateAction<AttachedFile[]>>;
    inputRows: number;
    placeholder: string;
    sendButtonText: string;
    fontSize: string;
    fontFamily: string;
    autoFocus: boolean;
    scrollToBottom: () => void;
}

// 重用 chat.tsx 中的 useSubmitHandler 逻辑
function useSubmitHandler() {
    const config = useAppConfig();
    const submitKey = config.submitKey;
    const isComposing = useRef(false);

    useEffect(() => {
        const onCompositionStart = () => {
            isComposing.current = true;
        };
        const onCompositionEnd = () => {
            isComposing.current = false;
        };

        window.addEventListener("compositionstart", onCompositionStart);
        window.addEventListener("compositionend", onCompositionEnd);

        return () => {
            window.removeEventListener("compositionstart", onCompositionStart);
            window.removeEventListener("compositionend", onCompositionEnd);
        };
    }, []);

    const shouldSubmit = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key !== "Enter") return false;
        if (e.key === "Enter" && (e.nativeEvent.isComposing || isComposing.current)) return false;
        return (
            (config.submitKey === SubmitKey.AltEnter && e.altKey) ||
            (config.submitKey === SubmitKey.CtrlEnter && e.ctrlKey) ||
            (config.submitKey === SubmitKey.ShiftEnter && e.shiftKey) ||
            (config.submitKey === SubmitKey.MetaEnter && e.metaKey) ||
            (config.submitKey === SubmitKey.Enter && !e.altKey && !e.ctrlKey && !e.shiftKey && !e.metaKey)
        );
    };

    return {
        submitKey,
        shouldSubmit,
    };
}

// 更新 InputContent 组件接收 props
const InputContent: React.FC<ChatInputProps> = (props) => {
    return (
        <div className={styles["bottom-content"]}>
            <ChatInput
                inputRef={props.inputRef}
                userInput={props.userInput}
                onUserInput={props.onUserInput}
                onKeyDown={props.onKeyDown}
                onPaste={props.onPaste}
                onSubmit={props.onSubmit}
                attachImages={props.attachImages}
                setAttachImages={props.setAttachImages}
                inputRows={props.inputRows}
                placeholder={props.placeholder}
                sendButtonText={props.sendButtonText}
                fontSize={props.fontSize}
                fontFamily={props.fontFamily}
                autoFocus={props.autoFocus}
                scrollToBottom={props.scrollToBottom}
            />
        </div>
    );
};

export function NewChat() {
    const navigate = useNavigate();
    const config = useAppConfig();
    const chatStore = useChatStore();
    const chatCommands = useChatCommand();

    // 1. 状态管理
    const inputRef = useRef<HTMLTextAreaElement>(null);
    const [userInput, setUserInput] = useState("");
    const [inputRows, setInputRows] = useState(2);
    const [attachImages, setAttachImages] = useState<AttachedFile[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    // 这些状态虽然当前组件中未直接使用，但保留以保持与chat组件一致的接口结构
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [promptHints] = useState<any[]>([]);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [autoScroll, setAutoScroll] = useState(false);
    const isMobileScreen = useMobileScreen();
    const { submitKey, shouldSubmit } = useSubmitHandler();

    // 2. 自动调整输入框高度
    const measure = useDebouncedCallback(
        () => {
            const rows = inputRef.current ? autoGrowTextArea(inputRef.current) : 1;
            const inputRows = Math.min(20, Math.max(2 + Number(!isMobileScreen), rows));
            setInputRows(inputRows);
        },
        100,
        {
            leading: true,
            trailing: true,
        }
    );

    // 自动调整输入框高度的副作用
    useEffect(() => {
        measure();
    }, [userInput, measure]);

    // 3. 提交处理函数
    // const doSubmit = useCallback(
    //     async (userInput: string) => {
    //         if (userInput.trim() === "" && isEmpty(attachImages)) return;

    //         const matchCommand = chatCommands?.match?.(userInput);
    //         if (matchCommand?.matched) {
    //             setUserInput("");
    //             matchCommand.invoke();
    //             return;
    //         }

    //         // 先保存用户输入，然后立即清空输入框
    //         const inputText = userInput;
    //         setUserInput("");

    //         setIsLoading(true);

    //         try {
    //             // 创建新会话
    //             await chatStore.newSession();
    //             const currentSession = chatStore.currentSession();

    //             // 创建用户消息并添加到对话中
    //             const userMessage = createMessage({
    //                 role: "user",
    //                 content: inputText,
    //             });

    //             // 添加用户消息到当前会话
    //             chatStore.updateTargetSession(currentSession, (session) => {
    //                 session.messages.push(userMessage);
    //             });

    //             // 使用相对路径请求，避免跨域问题
    //             const response = await fetch("/api/chat-proxy", {
    //                 method: "POST",
    //                 headers: {
    //                     "Content-Type": "application/json",
    //                 },
    //                 body: JSON.stringify({
    //                     userQuery: inputText,
    //                     metadata: {
    //                         enableThinking: false,
    //                     },
    //                 }),
    //             });

    //             // 检查响应
    //             if (!response.ok) {
    //                 throw new Error(`请求失败: ${response.status}`);
    //             }

    //             // 创建新的消息
    //             const newMessage = createMessage({
    //                 role: "assistant",
    //                 content: "",
    //             });

    //             // 添加消息到当前会话
    //             chatStore.updateTargetSession(currentSession, (session) => {
    //                 session.messages.push(newMessage);
    //             });

    //             // 处理返回的数据
    //             const reader = response.body?.getReader();
    //             if (reader) {
    //                 let jsonContent = ""; // 存储JSON内容
    //                 let textContent = ""; // 存储富文本内容
    //                 let isJsonComplete = false; // 标记JSON部分是否已完成

    //                 let isDone = false;
    //                 while (!isDone) {
    //                     const { done, value } = await reader.read();
    //                     if (done) {
    //                         isDone = true;
    //                         break;
    //                     }

    //                     // 解码数据
    //                     const text = new TextDecoder().decode(value);

    //                     // 处理SSE格式数据（移除data:前缀）
    //                     const lines = text.trim().split("\n");
    //                     const processedLines = lines
    //                         .map((line) => {
    //                             // 如果行以data:开头，去掉前缀
    //                             if (line.startsWith("data:")) {
    //                                 return line.substring(5);
    //                             }
    //                             return line;
    //                         })
    //                         .join("");

    //                     // 判断处理JSON还是富文本
    //                     if (!isJsonComplete) {
    //                         // 还在处理JSON部分
    //                         jsonContent += processedLines;

    //                         // 检查JSON是否已经完整（结束标记是JSON数组的右括号 "]"）
    //                         if (jsonContent.trim().endsWith("]")) {
    //                             isJsonComplete = true;

    //                             // 处理JSON数据内容
    //                             try {
    //                                 // 更新消息内容，只有JSON部分
    //                                 chatStore.updateTargetSession(currentSession, (session) => {
    //                                     const lastMessage = session.messages[session.messages.length - 1];
    //                                     if (lastMessage.id === newMessage.id) {
    //                                         lastMessage.content = jsonContent;
    //                                     }
    //                                 });
    //                             } catch (e) {
    //                                 console.error("JSON处理错误:", e);
    //                                 // 如果JSON解析出错，仍然将原始内容添加到消息中
    //                                 chatStore.updateTargetSession(currentSession, (session) => {
    //                                     const lastMessage = session.messages[session.messages.length - 1];
    //                                     if (lastMessage.id === newMessage.id) {
    //                                         lastMessage.content = jsonContent;
    //                                     }
    //                                 });
    //                             }
    //                         }
    //                     } else {
    //                         // 处理富文本部分
    //                         textContent += processedLines;

    //                         // 更新消息内容，JSON + 当前累积的富文本
    //                         chatStore.updateTargetSession(currentSession, (session) => {
    //                             const lastMessage = session.messages[session.messages.length - 1];
    //                             if (lastMessage.id === newMessage.id) {
    //                                 lastMessage.content = jsonContent + "\n\n" + textContent.trim();
    //                             }
    //                         });
    //                     }
    //                 }
    //             }

    //             // 导航到聊天界面
    //             navigate(Path.Chat);
    //         } catch (error: any) {
    //             console.error("API调用错误:", error);

    //             try {
    //                 const currentSession = chatStore.currentSession();
    //                 // 显示错误消息
    //                 const errorMessage = createMessage({
    //                     role: "assistant",
    //                     content: `发生错误: ${error.message}`,
    //                     isError: true,
    //                 });

    //                 chatStore.updateTargetSession(currentSession, (session) => {
    //                     session.messages.push(errorMessage);
    //                 });
    //             } catch (e) {
    //                 console.error("无法添加错误消息:", e);
    //             }

    //             // 仍然导航到聊天界面显示错误
    //             navigate(Path.Chat);
    //         } finally {
    //             setIsLoading(false);
    //             setAttachImages([]);
    //             chatStore.setLastInput(inputText);
    //             setAutoScroll?.(true);
    //         }
    //     },
    //     [chatStore, navigate, attachImages, chatCommands]
    // );
    const doSubmit = (userInput: any) => {
        message.info("暂不开放对话功能");
    };

    // 4. 用户输入处理
    const onInput = useCallback((text: string) => {
        setUserInput(text);
    }, []);

    // 5. 键盘按键处理
    const onInputKeyDown = useCallback(
        (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
            if (shouldSubmit(e) && !isLoading) {
                doSubmit(userInput);
                e.preventDefault();
            }
        },
        [shouldSubmit, doSubmit, userInput, isLoading]
    );

    // 6. 粘贴处理 (完整复制了 chat.tsx 中的相关逻辑)
    const handlePaste = useCallback(
        async (event: React.ClipboardEvent<HTMLTextAreaElement>) => {
            // 从配置中获取当前模型 (new chat 场景下使用默认模型)
            const currentModel = config.modelConfig.model;

            if (!isVisionModel(currentModel)) {
                return;
            }

            const items = (event.clipboardData || window.clipboardData).items;
            for (const item of items) {
                if (item.kind === "file" && item.type.startsWith("image/")) {
                    event.preventDefault();
                    const file = item.getAsFile();
                    if (file) {
                        // Current attachImages is AttachedFile[]
                        const currentAttachedFiles = [...attachImages];
                        try {
                            const dataUrl = await uploadImageRemote(file);
                            const newFile: AttachedFile = {
                                name: file.name,
                                size: formatFileSize(file.size),
                                type: file.type,
                                url: dataUrl,
                            };
                            currentAttachedFiles.push(newFile);

                            if (currentAttachedFiles.length > 3) {
                                currentAttachedFiles.splice(3, currentAttachedFiles.length - 3);
                            }
                            setAttachImages(currentAttachedFiles);
                        } catch (e) {
                            console.error("Failed to upload image:", e);
                        }
                    }
                }
            }
        },
        [attachImages, config.modelConfig.model, setAttachImages]
    );

    // 7. 滚动到底部 (在 new chat 视图中是空操作)
    const scrollToBottom = useCallback(() => {
        // 在 new chat 视图中无需实际滚动操作
    }, []);

    // 8. 自动聚焦配置
    const autoFocus = !isMobileScreen;

    // 准备传递给 InputContent 的所有属性
    const chatInputProps: ChatInputProps = {
        inputRef,
        userInput,
        onUserInput: onInput,
        onKeyDown: onInputKeyDown,
        onPaste: handlePaste,
        onSubmit: () => doSubmit(userInput),
        attachImages,
        setAttachImages,
        inputRows,
        placeholder: Locale.Chat.Input(submitKey),
        sendButtonText: Locale.Chat.Send,
        fontSize: config.fontSize.toString(),
        fontFamily: config.fontFamily,
        autoFocus,
        scrollToBottom,
    };

    return (
        <div className={styles["new-chat-container"]}>
            <div className={styles["new-chat"]}>
                <div className={styles["new-chat-content"]}>
                    <div className={styles["wrapper"]}>
                        <div className={styles["top-content"]}>
                            <div className={styles["content-wrapper"]}>
                                <div className={styles["wrapper-container"]}>
                                    <div className={styles["container-title-content"]}>
                                        <TitleContent />
                                    </div>
                                    <div className={styles["container-card-content"]}>
                                        <CardContent />
                                    </div>
                                </div>
                                <div className={styles["container-bottom"]}></div>
                            </div>
                        </div>
                    </div>
                    <InputContent {...chatInputProps} />
                </div>
            </div>
        </div>
    );
}
