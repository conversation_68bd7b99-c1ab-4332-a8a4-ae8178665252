.new-chat-container {
    padding: 0;
    box-sizing: border-box;
    display: flex;
    height: 100vh;
    position: relative;
    
    /* 针对容器内所有元素隐藏滚动条 */
    ::-webkit-scrollbar {
        width: 0 !important;
        height: 0 !important;
        display: none !important;
    }
    
    * {
        scrollbar-width: none !important; /* Firefox */
        -ms-overflow-style: none !important; /* IE and Edge */
    }
}
.new-chat {
    position: relative;
    background: no-repeat center url("../../icons/chat/chat-background.png");
    background-size: cover;
    display: flex;
    flex: 1;
    justify-content: center;
    overflow: hidden;
}
.new-chat-content {
    align-items: center;
    display: flex;
    position: relative;
    width: 100%;
    flex: 1;
    flex-direction: column;
}

.wrapper {
    flex-direction: column;
    overflow-y: auto;
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
    padding-bottom: 177px;    
    
    /* 隐藏滚动条但保留滚动功能 */
    &::-webkit-scrollbar {
        width: 0 !important;
        display: none !important;
    }
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
}

.top-content {
    max-width: 61.89%;
    padding: 124px 82px 0px;
    position: relative;
    width: 100%;

    .content-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        height: 100%;

        .wrapper-container {
            // max-width: 740px;
            width: 100%;
        
            .container-title-content {
                align-items: center;
                display: flex;
                flex-direction: column;
                padding: 0 40px;
            }

            .container-card-content {
                display: flex;
                flex-direction: column;
                flex-wrap: wrap;
                height: 230px;
                margin-top: 12px;
            }
        }

        .container-bottom {
            flex: none;
            height: 160px;
            width: 100%;
        }
    }
}

.bottom-content {
    // bottom: 8.73%;
    bottom: 78px;
    max-width: 61.89%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    position: absolute;
    width: 100%;
    min-height: 128px;
    z-index: 10;
    background-color: #fff;
    border-radius: 16px;
}

.title-content {
    display: flex;
    flex-direction: column;

    .title-top {
        display: flex;
        align-items: center;
        font-family: "Alibaba PuHuiTi, Alibaba PuHuiTi";
        font-weight: 500;
        font-size: 30px;
        color: #000000;
        line-height: 22px;
        text-align: left;

        .top-logo {
            margin-right: 18px;
        }
    }

    .title-bottom {
        font-weight: 500;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.42);
        margin-top: 22px;
    }
}

.title-top {
    color: #000;
    font-family: "Alibaba PuHuiTi";
    font-size: 30px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .top-logo {
        margin-right: 22px;
    }
}
.title-bottom {
    color: rgba(0, 0, 0, 0.42);
    font-family: "PingFang SC";
    font-size: 16px;
    font-weight: 500;
}

.card-content {
    display: flex;
    align-items: stretch;
    justify-content: space-between;
    margin-top: 6.55%;
    width: 100%;

    .left-card {
        display: flex;
        flex-direction: column;
        width: 58%;
        margin-right: 20px;

        .left-match {
           padding: 20px;
           background: #ffffff;
           border-radius: 12px;

           display: flex; 
           flex-direction: column; 
           flex-grow: 1; 

            .match-top {
                display: flex;
                justify-content: space-between;

                .title {
                    font-weight: 600;
                    font-size: 14px;
                    color: rgba(0,0,0,0.92);
                }
                .btn {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    font-size: 12px;
                    color: #0099f2;
                    cursor: pointer;
                    margin-bottom: 20px;
                }
            }

            .match-content {
                background: #f8f9fb;
                border-radius: 12px;
                padding: 16px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                cursor: pointer;
                overflow: auto;

                .match-desc {
                    display: flex;
                    align-items: center;

                    .content {
                        display: flex;
                        flex-direction: column;
                    }

                    .match-logo {
                        width: 30px;
                        height: 30px;
                        background: no-repeat center url("../../icons/chat/test-card4.png");
                        border-radius: 8px;
                        padding: 8px;
                        margin-right: 8px;
                    }

                    .match-station {
                        font-weight: 600;
                        font-size: 14px;
                        color: rgba(0,0,0,0.92);
                        margin-bottom: 6px;
                    }
                    .match-user {
                        font-weight: 400;
                        font-size: 14px;
                        color: rgba(0,0,0,0.44);
                        line-height: 20px;
                    }
                }
                .match-count {
                    display: flex;
                    flex-direction: column;
                    .count {
                        font-weight: 600;
                        font-size: 20px;
                        color: #f5ab00;
                        line-height: 20px;
                    }
                    .text {
                        font-weight: 400;
                        font-size: 12px;
                        color: #bdbdbd;
                        line-height: 20px;
                        margin-top: 12px;
                    }
                }
            }
        }

        .left-file {
            margin-top: 12px;
            padding: 16px 20px;
            background: linear-gradient(180deg, #F5FCFF 0%, rgba(250, 250, 250, 0) 80.26%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            position: relative;
            z-index: 1;
            overflow: hidden;
            
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #ffffff;
                z-index: -1;
            }

            .user {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 14px;
                color: rgba(0,0,0,0.68);
            }

            .desc {
                font-size: 13px;
                color: rgba(0,0,0,0.44);
                line-height: 20px;
            }
        }
       
    }

    .right-card {
        width: 42%;
        padding: 20px;
        background: linear-gradient(180deg, #F5FCFF 0%, rgba(250, 250, 250, 0) 80.26%);
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        position: relative;
        z-index: 1;
        overflow: hidden;
        
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #F5FCFF;
            z-index: -1;
        }

        .right-title {
            height: 20px;
            font-weight: 600;
            font-size: 14px;
            color: rgba(0,0,0,0.92);
            margin-bottom: 24px;
        }
        .right-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
            flex: 1;
        }
    }
}
