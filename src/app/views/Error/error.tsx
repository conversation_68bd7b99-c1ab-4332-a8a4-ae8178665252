"use client";
// 错误边界处理的组件 错误组件必须是客户端组件
import React from "react";
// import { IconButton } from "./shared/button";
// import ResetIcon from "../icons/reload.svg";
// import Locale from "../locales";
import { useSyncStore } from "../../store/sync";
import { useChatStore } from "../../store/chat";
import { Result } from "antd";

interface IErrorBoundaryState {
    hasError: boolean;
    error: Error | null;
    info: React.ErrorInfo | null;
}

export class ErrorBoundary extends React.Component<any, IErrorBoundaryState> {
    constructor(props: any) {
        super(props);
        this.state = { hasError: false, error: null, info: null };
    }

    componentDidCatch(error: Error, info: React.ErrorInfo) {
        // Update state with error details
        this.setState({ hasError: true, error, info });
    }

    clearAndSaveData() {
        try {
            useSyncStore.getState().export();
        } finally {
            useChatStore.getState().clearAllData();
        }
    }

    render() {
        if (this.state.hasError) {
            // Render error message
            return (
                <div className="error">
                    <h2>Oops, something went wrong!</h2>
                    {/* <pre>
                        <code>{this.state.error?.toString()}</code>
                        <code>{this.state.info?.componentStack}</code>
                    </pre> */}
                    <Result status="error" title="发生错误" subTitle="当前页面发生错误"></Result>
                </div>
            );
        }
        // if no error occurred, render children
        return this.props.children;
    }
}
