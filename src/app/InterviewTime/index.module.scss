.container {
    padding: 16px;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    background-color: #fff;
    transition: all 0.3s ease;
    // gap: 20px;

    &--fullscreen {
        padding: 20px;
    }

    .breadcrumb-wrapper {
        margin-bottom: 0;
    }

    .container-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: rgba(0, 0, 0, 0.86);
        font-family: "PingFang SC";
        font-weight: 500;
        font-size: 18px;
        line-height: 25px;
        margin-bottom: 20px;
        flex-shrink: 0;
    }

    .container-content {
        flex: 1;
        // overflow: auto;
        min-height: 0;
        display: flex;
        flex-direction: column;
        
        > * {
            flex: 1;
            min-height: 0;
        }
    }
}