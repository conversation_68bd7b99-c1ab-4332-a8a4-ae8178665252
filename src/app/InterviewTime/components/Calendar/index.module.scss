.calendar {
    display: flex;
    flex-direction: column;
    // height: 100%;
  .fc {
    font-size: 14px;
    
    .fc-toolbar {
      margin-bottom: 1em;
      
      .fc-toolbar-title {
        font-size: 1.2em;
        font-weight: 500;
      }
      
      .fc-button {
        padding: 0.25em 0.5em;
        font-size: 0.9em;
        border-radius: 4px;
        background-color: #1890ff;
        border-color: #1890ff;
        
        &:hover {
          background-color: #40a9ff;
          border-color: #40a9ff;
        }
        
        &:disabled {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: #00000040;
        }
      }
    }
    
    .fc-event {
      cursor: pointer;
      border-radius: 3px;
      padding: 2px 4px;
      
      .fc-event-title {
        font-weight: 500;
      }
      
      .fc-event-time {
        font-weight: 400;
        font-size: 0.85em;
      }
    }
    
    .fc-daygrid-event {
      border-radius: 3px;
      margin: 1px 0;
    }
    
    .fc-timegrid-event {
      border-radius: 3px;
      .fc-event-main {
        padding: 1px 2px;
      }
    }
    
    .fc-scrollgrid {
      border-color: #f0f0f0;
    }
    
    .fc-scrollgrid-section > * {
      border-color: #f0f0f0;
    }
    
    th {
      background-color: #fafafa;
      border-color: #f0f0f0;
      font-weight: 500;
    }
    
    td {
      border-color: #f0f0f0;
    }
    
    .fc-day-today {
      background-color: rgba(24, 144, 255, 0.05) !important;
    }
    
    .fc-now-indicator {
      background-color: #ff4d4f;
    }
    
    .fc-timegrid-now-indicator-line {
      border-color: #ff4d4f;
    }
    
    // 时间网格样式
    .fc-timegrid-slot {
      border-color: #f0f0f0;
    }
    
    .fc-timegrid-axis {
      border-color: #f0f0f0;
    }
    
    .fc-col-header-cell {
      background-color: #f5f5f5;
    }
    
    // 工作时间样式
    .fc-non-business {
      background-color: #f5f5f5;
    }
    
    // 选择区域样式
    .fc-highlight {
      background-color: rgba(220, 245, 227, 0.5);
    }
  }
}

// 确保在全屏模式下日历正确显示
:global(.fullscreen) {
  .calendar {
    height: 100%;
    
    .fc {
      height: 100%;
    }
  }
} 

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
}

.calendar-header-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.calendar-day-header-date {
    font-family: "PingFang SC";
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
}

.calendar-day-header-week {
    font-family: "PingFang SC";
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
}

.slot-label {
    color: rgba(0, 0, 0, 0.6);
    font-family: "Alibaba PuHuiTi";
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
}

.event-title {
    color: rgba(17, 174, 65, 1);
    font-family: "Alibaba PuHuiTi";
    font-weight: 500;
    font-size: 14px;
    line-height: 19px;
    padding: 8px;
    box-sizing: border-box;

    .event-title-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        :global(.ant-btn.ant-btn-icon-only ) {
            color: rgba(17, 174, 65, 1);
        }
    }
}

.month-event-content {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0 4px;

    .month-event-content-point {
        width: 6px;
        height: 6px;
        background-color: rgba(19, 191, 71, 1);
        border-radius:50%
    }

    .month-event-content-text {
        flex: 1;
        min-width: 0;
        color: rgba(0, 0, 0, 0.86);
        font-family: "PingFang SC";
        font-weight: 500;
        font-size: 12px;
        line-height: 17px;
    }
}

// 短时间事件的特殊样式
.event-title-short {
    .event-title-header {
        min-height: 16px;
        
        .event-title-room {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex: 1;
            margin-right: 4px;
        }
    }
}

// // 移除时间网格的横线
// .calendar :global(.fc-timegrid-slot) {
//     border-bottom: none !important;
// }

// .calendar :global(.fc-day-other .fc-daygrid-day-top) {
//     border-bottom: none !important;
// }

// .calendar :global(.fc-timegrid-slots tr) {
//     border-bottom: none !important;
// }

// 为第一行表格头添加灰色背景
.calendar :global(.fc-col-header-row) {
    background-color: #f5f5f5 !important;
}

.calendar :global(.fc-timegrid-axis) {
    background-color: #f5f5f5 !important;
}

.calendar :global(.fc-col-header-cell) {
    background-color: #f5f5f5 !important;
    height: 40px !important;
    padding-left: 16px;
    vertical-align: middle;
    // 表头左对齐
    text-align: left !important;
}

// .calendar :global(.fc-col-header-cell *) {
//     text-align: left !important;
// }

// 设置表头行高度为40px
.calendar :global(.fc-col-header-row) {
    height: 40px !important;
    // text-align: center;
    vertical-align: middle;
}

// 设置所有单元格高度
.calendar :global(.fc-timegrid-slot) {
    height: 25px !important;
}

.calendar :global(.fc-timegrid-slot-minor) {
    height: 25px !important;
}

.calendar :global(.fc-timegrid-slot-major) {
    height: 25px !important;
}

// 移除时间网格事件的阴影
.calendar :global(.fc-timegrid-event-harness-inset .fc-timegrid-event),
.calendar :global(.fc-timegrid-event.fc-event-mirror),
.calendar :global(.fc-timegrid-more-link) {
    box-shadow: none !important;
}

// Period A (绿色) - 周视图边框
.calendar :global(.fc-timeGridWeek-view .event-period-a),
.calendar :global(.fc-timeGridWeek-view .event-period-a .fc-timegrid-event),
.calendar :global(.fc-timeGridWeek-view .event-period-a.fc-event-mirror) {
    border-top: 2px solid rgba(17, 174, 65, 1) !important;
}

// Period A (绿色) - 日视图边框
.calendar :global(.fc-timeGridDay-view .event-period-a),
.calendar :global(.fc-timeGridDay-view .event-period-a .fc-timegrid-event),
.calendar :global(.fc-timeGridDay-view .event-period-a.fc-event-mirror) {
    border-left: 2px solid rgba(17, 174, 65, 1) !important;
}

// Period B (蓝色) - 周视图边框
.calendar :global(.fc-timeGridWeek-view .event-period-b),
.calendar :global(.fc-timeGridWeek-view .event-period-b .fc-timegrid-event),
.calendar :global(.fc-timeGridWeek-view .event-period-b.fc-event-mirror) {
    border-top: 2px solid #0099f2 !important;
}

// Period B (蓝色) - 日视图边框
.calendar :global(.fc-timeGridDay-view .event-period-b),
.calendar :global(.fc-timeGridDay-view .event-period-b .fc-timegrid-event),
.calendar :global(.fc-timeGridDay-view .event-period-b.fc-event-mirror) {
    border-left: 2px solid #0099f2 !important;
}

// Period C (红色) - 周视图边框
.calendar :global(.fc-timeGridWeek-view .event-period-c),
.calendar :global(.fc-timeGridWeek-view .event-period-c .fc-timegrid-event),
.calendar :global(.fc-timeGridWeek-view .event-period-c.fc-event-mirror) {
    border-top: 2px solid #f54d46 !important;
    cursor: pointer !important;
}

// Period C (红色) - 日视图边框
.calendar :global(.fc-timeGridDay-view .event-period-c),
.calendar :global(.fc-timeGridDay-view .event-period-c .fc-timegrid-event),
.calendar :global(.fc-timeGridDay-view .event-period-c.fc-event-mirror) {
    border-left: 2px solid #f54d46 !important;
    cursor: pointer !important;
}

// Period B 的文字和图标颜色覆盖
.calendar :global(.event-period-b) {
    .event-title {
        color: #0099f2 !important;
        
        .event-title-header {
            :global(.ant-btn.ant-btn-icon-only) {
                color: #0099f2 !important;
            }
        }
    }
}

// Period C 的文字和图标颜色覆盖
.calendar :global(.event-period-c) {
    .event-title {
        color: #b6514d !important;
        
        .event-title-header {
            :global(.ant-btn.ant-btn-icon-only) {
                color: #b6514d !important;
            }
        }
    }
}

// 通用事件边框样式 - 确保在所有状态下都生效
.calendar :global {
    // 周视图特定样式
    .fc-timeGridWeek-view .event-period-a.fc-event {
        border-left: none !important;
        border-top: 2px solid rgba(17, 174, 65, 0.7) !important;
    }
    
    .fc-timeGridWeek-view .event-period-b.fc-event {
        border-left: none !important;
        border-top: 2px solid #0099f2 !important;
    }
    
    // 日视图特定样式
    .fc-timeGridDay-view .event-period-a.fc-event {
        border-top: none !important;
        border-left: 2px solid rgba(17, 174, 65, 0.7) !important;
    }
    
    .fc-timeGridDay-view .event-period-b.fc-event {
        border-top: none !important;
        border-left: 2px solid #0099f2 !important;
    }
}

// 月视图的今日高亮去除
.calendar :global(.fc-timegrid-col.fc-day-today) {
    background-color: transparent !important;
}

.calendar :global(.fc .fc-daygrid-day.fc-day-today ) {
    background-color: transparent !important;
}

.calendar :global(.fc-direction-ltr .fc-timegrid-col-events ) {
    margin: 0 !important;
}

.calendar :global(.fc .fc-daygrid-day-top ) {
    flex-direction: row !important;
}

.calendar :global(.fc-theme-standard .fc-popover-header) {
    background: none !important;
    padding: 12px 12px 0px !important;
    color: rgba(0, 0, 0, 0.6);
    font-family: "PingFang SC";
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
}

.calendar :global(.fc .fc-more-popover .fc-popover-body ) {
    padding: 0 8px 8px !important;
    min-width: 200px !important;
    max-width: 300px !important;
}

// 月视图查看更多的弹窗样式
.calendar :global(.fc-theme-standard .fc-popover) {
    border-radius: 12px;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.12);
}

.calendar :global(.fc-timegrid-slot-minor) {
    // border: unset !important;
    border: 1px dashed rgba(0, 0, 0, 0.08);
}

.calendar :global(.fc .fc-timegrid-slot-label) {
    border: none !important;
    background: #fff !important;
    border-right: 1px solid rgba(0, 0, 0, 0.08) !important;
}

// .calendar :global(.fc-popover-body .fc-daygrid-event-harness) {

//     &:first {
//         margin-top: 4px !important;
//     }
// }

// 月视图的日期数字样式
.month-day-number {
    @extend.calendar-day-header-date;
    text-align: left !important;
    padding: 8px 8px 0;
}

.month-today-highlight {
    background-color: rgba(0, 153, 242, 1);
    color: #fff;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px;
}

.more-link-content {
    color: rgba(0, 0, 0, 0.6);
    font-family: "PingFang SC";
    font-weight: 400;
    font-size: 12px;
    line-height: 17px;
}

.calendar-date-picker {
    
    input {
        font-family: "Alibaba PuHuiTi" !important;
        font-weight: 400 !important;
        font-size: 18px !important;
        line-height: 25px !important;
    }
}

.calendar :global(.fc-scroller) {
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
        background-color: rgba(0, 0, 0, 0.3);
    }

    // For Firefox
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.month-popover :global(.ant-popover-inner) {
    padding: 8px;
}

.month-popover-content {
    .loading-container {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 24px;
        color: rgba(0, 0, 0, 0.6);
    }

    .slots-container {
        max-height: 200px;
        overflow-y: auto;
        padding: 8px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .slot-button {
        width: 100%;
        text-align: center;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        &:hover {
            border-color: #1677ff;
            color: #1677ff;
        }
    }

    .no-slots {
        padding: 24px;
        text-align: center;
        color: rgba(0, 0, 0, 0.45);
    }
}

