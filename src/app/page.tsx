"use client";

require("@/polyfill");
import { useEffect, useState } from "react";
import LogoIcon from "@/icons/chat/title-logo.svg";
import LoadingIcon from "@/icons/three-dots.svg";
import { getCSSVar } from "@/utils";
import { SlotID } from "@/constant";
import { ErrorBoundary } from "@/components/Error/error";
import { SideBar } from "@/components/LeftSidebar/index";
import { useAppConfig } from "@/store/config";
import { getClientConfig } from "@/config/client";
import { getClientApi } from "@/client/api";
import { useAccessStore } from "@/store";
import clsx from "clsx";
import RightSidebar from "@/components/RightSidebar";
import styles from "./views/Home/home.module.scss";

export function Loading(props: { noLogo?: boolean }) {
    return (
        <div className={clsx("no-dark", styles["loading-content"])}>
            {!props.noLogo && <LogoIcon />}
            <LoadingIcon />
        </div>
    );
}

export default function HomePage() {
    const config = useAppConfig();
    const isHome = true; // This is the home page
    const [loadingApi, setLoadingApi] = useState(true);

    useEffect(() => {
        getClientApi().then(() => {
            setLoadingApi(false);
        });
    }, []);

    useEffect(() => {
        console.log("[Config] got config from build time", getClientConfig());
        useAccessStore.getState().fetch();
    }, []);

    if (loadingApi) {
        return <Loading />;
    }

    return (
        <ErrorBoundary>
            <div
                className={clsx(
                    "main-container",
                    styles.container,
                    {
                        "tight-container": config.tightBorder,
                    },
                    getCSSVar("--window-width"),
                    getCSSVar("--window-height")
                )}
            >
                <SideBar className={isHome ? styles["sidebar-show"] : ""} />

                <div className={styles["window-content"]} id={SlotID.AppBody}>
                    <div className={styles["home-content"]}>
                        <div className={styles["home-header"]}>
                            <h1>Welcome to InovanceChat</h1>
                            <p>Your AI-powered conversation platform</p>
                        </div>
                    </div>
                </div>

                <RightSidebar />
            </div>
        </ErrorBoundary>
    );
}
