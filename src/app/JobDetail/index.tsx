import Header from "@/app/components/Header";
import {
    getAllTagsApi,
    getFlowDetailApi,
    getJobFlowChildrenByIdApi,
    getJobFlowDetailApi,
    getJobPublishByJobSpecsApi,
} from "@/app/request/modules/dashboard";
import useJobFlowStore, {
    Department,
    FlowDetailResp,
    FlowItemParams,
    FlowNodeDefinition,
    FlowRecord,
    FlowStep,
    JobFlowReq,
    MyFlowResp,
    NodeInstance,
    TagsItem,
    TagsResp,
} from "@/app/store/modules/dashboard/jobFlow";
import { RespParams } from "@/app/typing";
import { Button, Divider, Empty, Flex, Layout, Steps, Table, Tabs, Tag } from "antd";
import { useEffect, useMemo, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
    assignmentList,
    hiringBatchData,
    JobTags,
    jobTypeData,
    statusList as JobStatusList,
} from "../Dashboard/constant";
import { cloneDeep } from "lodash-es";
import { JOB_TYPE, Path, TODO_STATUS } from "@/app/constant";
import styles from "./index.module.scss";
import InterviewStage from "@/app/components/InterviewStage";
import UseCandidateStore, { InterviewFlowResp, TrackStageStatistic } from "@/app/store/modules/candidate";
import { getInterviewFlowApi } from "@/app/request/modules/candidate";
import { TextView } from "@/app/components/TextView";
import JobTagsComp from "@/app/views/Dashboard/AddFlow/JobTags";
import RecruitmentProcess from "@/app/views/Dashboard/JobManage/Detail/RecruitmentProcess";
import useJobDetailStore from "@/app/store/modules/dashboard/jobDetail";
import { ColumnProps } from "antd/es/table";

const JobDetail = () => {
    const location = useLocation();
    const navigate = useNavigate();

    const [flowInstanceId, setFlowInstanceId] = useState<string>("");
    const [jobId, setJobId] = useState<string>("");
    const [currentFlow, setCurrentFlow] = useState<NodeInstance | null>(null);
    const [tagsList, setTagsList] = useState<TagsResp[]>([]);
    // 审批流程步骤数据
    const [flowSteps, setFlowSteps] = useState<FlowStep[]>([]);
    // 审批记录表格数据
    const [dataSource, setDataSource] = useState<FlowRecord[]>([]);
    const [jobInfo, setJobInfo] = useState<JobFlowReq>();
    const [jobPublishTableData, setJobPublishTableData] = useState<MyFlowResp[]>([]);

    const { statusList, currentStage, setStatusList, setCurrentStage } = UseCandidateStore((state) => ({
        statusList: state.statusList,
        currentStage: state.currentStage,
        setStatusList: state.setStatusList,
        setCurrentStage: state.setCurrentStage,
    }));
    const { flowDetail, getFlowDetail, setDetailInfo } = useJobDetailStore((state) => ({
        flowDetail: state.flowDetail,
        getFlowDetail: state.getFlowDetail,
        setDetailInfo: state.setDetailInfo,
    }));

    const tableColumns: ColumnProps<MyFlowResp>[] = [
        {
            title: "需求部门",
            dataIndex: "orgName",
            width: 300,
            ellipsis: true,
        },
        {
            title: "职位名称",
            dataIndex: "jobNameInner",
            width: 300,
            ellipsis: true,
        },
        {
            title: "对外职位名称",
            dataIndex: "jobNameOuter",
            width: 250,
            ellipsis: true,
        },
        {
            title: "招聘批次",
            dataIndex: "hiringBatch",
            width: 120,
        },
        {
            title: "状态",
            dataIndex: "status",
            width: 120,
            render: (_, record) => {
                const obj = JobStatusList.find((item) => item.value === record.status);
                return <Tag color={obj?.type}>{obj?.label}</Tag>;
            },
        },
        {
            title: "招聘岗位数",
            dataIndex: "specIds",
            width: 120,
            render: (_, record) => {
                return record.specIds?.split(",")?.length;
            },
        },
        {
            title: "需求人数",
            dataIndex: "hiringPersonNum",
            width: 120,
        },
        {
            title: "需求负责人",
            dataIndex: "createUser",
            width: 150,
            render: (_, record) => {
                return record.createUser?.empId + "-" + record.createUser?.empName;
            },
        },
    ];

    useEffect(() => {
        getAllTagsList();
    }, []);
    useEffect(() => {
        setFlowInstanceId(location.state.flowInstanceId);
    }, [location.state.flowInstanceId]);
    useEffect(() => {
        setJobId(location.state.jobId);
        getJobPublishList(location.state.jobId);
    }, [location.state.jobId]);
    useEffect(() => {
        setDetailInfo({ orgId: location.state.orgId });
    }, [location.state.orgId]);

    useEffect(() => {
        if (!tagsList) return;
        if (flowInstanceId) {
            getJobFlowDetail(flowInstanceId);
            return;
        }
        // 流程不存在时，直接查询需求详情
        if (jobId) {
            getFlowFormData(jobId, JOB_TYPE.jobSpec);
            getInterviewFlowList();
            getFlowDetail(Number(jobId));
        }
    }, [tagsList]);

    const getJobInnerName = useMemo(() => {
        const nameList = jobInfo?.jobNameInner.split("/");
        return nameList?.[nameList?.length - 1] ?? "";
    }, [jobInfo?.jobNameInner]);

    const basicInfo = useMemo(() => {
        const result = [
            { title: "职位名称", value: jobInfo?.jobNameInner },
            { title: "对外职位名称", value: jobInfo?.jobNameOuter },
            { title: "需求部门", value: jobInfo?.orgName },
            { title: "招聘批次", value: hiringBatchData.find((item2) => item2.value === jobInfo?.hiringBatch)?.label },
            { title: "目标人数", value: jobInfo?.hiringPersonNum },
            { title: "offer目标", value: jobInfo?.offerTarget },
            { title: "职位类别", value: jobTypeData.find((item2) => item2.value === jobInfo?.jobType)?.label },
            { title: "原因说明", value: jobInfo?.reason },
            { title: "拔尖人才(%)", value: jobInfo?.topTalents?.toFixed(2) },
            { title: "优秀人才(%)", value: jobInfo?.excellentTalents?.toFixed(2) },
            { title: "普通人才(%)", value: jobInfo?.regularTalents?.toFixed(2) },
            { title: "工作地点", value: jobInfo?.jobLocation },
            { title: "学历要求", value: jobInfo?.educational },
            { title: "语言要求", value: jobInfo?.language },
        ];

        if (jobInfo?.allocationMethod) {
            result.push({
                title: "分配方式",
                value: assignmentList.find((item2) => item2.value === jobInfo?.allocationMethod)?.label,
            });
        }

        return result;
    }, [jobInfo]);

    const getAllTagsList = async () => {
        const res2: RespParams<TagsResp[]> = await (await getAllTagsApi({ layer: "", label: "" })).json();
        if (res2.code === 200) {
            setTagsList(res2.data);
        }
    };

    const getInterviewFlowList = async () => {
        try {
            const res: RespParams<InterviewFlowResp> = await (await getInterviewFlowApi(Number(jobId))).json();
            if (res.code === 200) {
                const list: TrackStageStatistic[] = [
                    {
                        stageId: "all",
                        stageName: "全部",
                        stageCode: "all",
                        total: res.data.total,
                        trackStateStatistics: [],
                    },
                    {
                        stageId: "discard",
                        stageName: "已终止",
                        stageCode: "discard",
                        total: res.data.totalOut,
                        trackStateStatistics: [],
                    },
                    ...res.data.trackStageStatistics.map((item) => {
                        item.trackStateStatistics.unshift({
                            stateId: "",
                            stateName: "全部",
                            stateCode: "",
                            total: item.total,
                        });

                        item.currentState = item.trackStateStatistics?.[0];
                        return item;
                    }),
                ];
                setStatusList(list);
            }
        } catch (error) {
            setStatusList([]);
        }
    };

    const getJobPublishList = async (jobId: number) => {
        const res: RespParams<MyFlowResp> = await (await getJobPublishByJobSpecsApi(jobId)).json();
        if (res.code === 200) {
            setJobPublishTableData(res?.data ? [res.data] : []);
        }
    };

    //获取流程详情
    const getJobFlowDetail = async (id: string) => {
        try {
            const res: RespParams<FlowDetailResp> = await (await getFlowDetailApi(id)).json();
            // 处理流程步骤
            // const currentNode = res.data.currentNodeInstance?.nodeId;
            setCurrentFlow(res.data.currentNodeInstance);
            const flowList = res.data.flowNodeDefinitions?.map((item: FlowNodeDefinition) => {
                let currentStatus = "wait";
                // 从后往前查找，
                const current = res.data.nodeInstanceList.findLast(
                    (item2) => item2.nodeId === item.id && !item2?.parentNodeInstanceId
                );
                if (current) {
                    if (Object.values(TODO_STATUS).includes(current.status)) {
                        switch (current.status) {
                            case TODO_STATUS.process:
                                currentStatus = "process";
                                break;
                            case TODO_STATUS.done:
                                currentStatus = "finish";
                                break;
                            case TODO_STATUS.close:
                                currentStatus = "error";
                                break;
                            default:
                                currentStatus = "wait";
                                break;
                        }
                    }
                }

                return {
                    title: item.name,
                    description: item?.description ?? "默认描述",
                    status: currentStatus,
                };
            });

            setFlowSteps(flowList as FlowStep[]);

            // 处理审批记录
            const flowRecordList = res.data.nodeInstanceList?.map((item: any, index: number) => {
                let comment = item.comment;
                if (item.status === 3 && res.data.nodeInstanceList.length > index + 1) {
                    comment += `（转办人：${
                        res.data.nodeInstanceList[index + 1].approver
                            ? `${res.data.nodeInstanceList[index + 1].approver?.emp_id}-${
                                  res.data.nodeInstanceList[index + 1].approver?.emp_name
                              }`
                            : "暂无"
                    }）`;
                }

                return {
                    id: item.id,
                    key: item.id,
                    time: item.startedAt,
                    node: item.nodeDefinition?.name,
                    operator: item.approver ? `${item.approver?.emp_id}-${item.approver?.emp_name}` : "暂无",
                    action: item.statusStr,
                    remark: comment,
                    result: "",
                    parentNodeInstanceId: item?.parentNodeInstanceId ?? "",
                    nodeTypeStr: item.nodeDefinition?.nodeTypeStr,
                };
            });

            // 构建树形
            const buildTree = (items: any[]) => {
                const itemMap = new Map();
                items.forEach((item) => {
                    itemMap.set(item.id, { ...item, children: [] });
                });

                // 构建树
                const rootItems: any[] = [];
                items.forEach((item) => {
                    const mappedItem = itemMap.get(item.id);
                    if (item.parentNodeInstanceId && itemMap.has(item.parentNodeInstanceId)) {
                        // 如果有父节点，将此项添加到父节点的 children 中
                        const parent = itemMap.get(item.parentNodeInstanceId);
                        parent.children.push(mappedItem);
                    } else {
                        // 如果没有父节点或父节点不存在，则为根节点
                        rootItems.push(mappedItem);
                    }
                });

                // 清理空的 children 数组
                const cleanEmptyChildren = (node: any) => {
                    if (node.children.length === 0) {
                        delete node.children;
                    } else {
                        node.children.forEach(cleanEmptyChildren);
                    }
                    return node;
                };

                return rootItems.map(cleanEmptyChildren);
            };

            const treeData = flowRecordList ? buildTree(flowRecordList) : [];
            setDataSource(treeData);
            if (res.data.relatedForm) {
                getFlowFormData(res.data.relatedForm, JOB_TYPE.jobSpec);
                if (JOB_TYPE.jobSpec) {
                    getFlowDetail(Number(res.data.relatedForm));
                    getInterviewFlowList();
                }
            }
            // setCurrentJobForm({
            //     flowId: res.data.flowId,
            //     relatedForm: res.data.relatedForm,
            // });
            // if (TaskTypeList.map((item) => item.key).includes(type ?? "")) {
            //     getFlowData(res?.data?.relatedForm, res.data.flowId);
            // }
        } catch (error) {
            console.log(error);
        }
    };

    // 获取需求详情
    const getFlowFormData = async (formId: string, flowType: JOB_TYPE) => {
        const res: RespParams<JobFlowReq> = await (
            await (flowType === JOB_TYPE.jobSpec
                ? getJobFlowDetailApi(formId)
                : getJobFlowChildrenByIdApi(Number(formId)))
        ).json();
        if (res.code === 200) {
            let list: JobFlowReq[] = [];
            list.push(res.data);

            list = list.map((item) => {
                item.orgId = Array.isArray(item.orgId) ? item.orgId : item.orgId.split("/");
                item.jobNameInnerPath = Array.isArray(item.jobNameInnerPath)
                    ? item.jobNameInnerPath
                    : item.jobNameInnerPath.split("/");
                item.educational = Array.isArray(item.educational) ? item.educational : item.educational.split("/");
                item.jobLocation = Array.isArray(item.jobLocation) ? item.jobLocation : item.jobLocation.split("/");

                item.jobTags = cloneDeep(JobTags);
                const tagsDetail: TagsResp[] = [];
                item.tags?.forEach((item2: number) => {
                    const tagItem = tagsList.find((item3) => item3.id === item2);
                    if (tagItem) {
                        tagsDetail.push(tagItem);
                    }
                });

                tagsDetail.forEach((item2) => {
                    const groupIndex = item.jobTags?.findIndex((item3: TagsItem) => item3.label === item2.layer) ?? -1;
                    if (groupIndex !== -1) {
                        const tagIndex =
                            item.jobTags?.[groupIndex]?.children?.findIndex(
                                (item3: any) => item3.label === item2.label
                            ) ?? -1;

                        if (tagIndex !== -1) {
                            item.jobTags?.[groupIndex]?.children?.[tagIndex]?.value?.push(item2);
                        }
                    }
                });
                item.editMode = false;

                if (item.jobSpecs && item.jobSpecs.length > 0) {
                    item.jobSpecs.forEach((item2: FlowItemParams) => {
                        item2.assignment = parseInt((item2 as any).proportionValues);
                    });
                }

                return item;
            });

            setJobInfo(list?.[0]);
        }
    };

    const handleEditJob = () => {
        navigate(Path.JobEdit, {
            state: {
                jobId: jobId,
                orgId: jobInfo?.orgId,
            },
        });
    };

    // 职位信息
    const JobInfo = () => {
        return (
            <div className={styles["flow-job-detail-content"]}>
                <div className={styles["flow-job-detail-basic"]}>
                    {basicInfo.map((item, index) => (
                        <div key={index} className={styles["flow-job-detail-basic-item"]}>
                            <div className={styles["title"]}>{item.title}</div>
                            <div className={styles["value"]}>
                                <TextView text={(item?.value ?? "").toString()} />
                            </div>
                        </div>
                    ))}
                </div>
                {/* <div style={{ margin: "12px 0" }}>
                    {jobInfo?.jobSpecs && jobInfo?.jobSpecs.length > 0 && (
                        <AddFlowDetailChildren
                            tableData={item.jobSpecs as MyFlowResp[]}
                            isEdit={false}
                            allocationMethod={item.allocationMethod ?? 0}
                        />
                    )}
                </div> */}
                <div className={styles["flow-job-detail-job-jd"]}>
                    <div className={styles["title"]}>岗位JD描述</div>
                    <div className={`${styles["value"]} ${styles["jd-content"]}`}>{jobInfo?.jobJD}</div>
                </div>
                {jobInfo?.jobTags && (
                    <JobTagsComp tagsList={jobInfo?.jobTags} editable={false} handleChangeTags={() => {}} />
                )}
            </div>
        );
    };
    // 流程历史信息
    const FlowInfo = () => {
        if (flowSteps.length === 0) return <Empty />;

        return (
            <Flex vertical>
                <Steps
                    style={{ padding: "0 20%", marginBottom: 16 }}
                    current={2}
                    status="process"
                    size="small"
                    labelPlacement="vertical"
                    items={flowSteps.map((step) => ({
                        title: step.title,
                        description: step.description,
                        status: step.status,
                    }))}
                />
                <Table
                    rowKey="id"
                    columns={[
                        { title: "时间", dataIndex: "time", key: "time", width: 200, ellipsis: true },
                        { title: "节点名称", dataIndex: "node", key: "node", width: 180 },
                        {
                            title: "节点类型",
                            dataIndex: "nodeTypeStr",
                            key: "nodeTypeStr",
                            width: 120,
                        },
                        { title: "审批人", dataIndex: "operator", key: "operator", width: 150 },
                        {
                            title: "审批状态",
                            dataIndex: "action",
                            key: "action",
                            width: 120,
                        },
                        { title: "处理意见", dataIndex: "remark", key: "remark", width: 200 },
                    ]}
                    dataSource={dataSource}
                    pagination={false}
                />
            </Flex>
        );
    };

    const tabsItems = [
        {
            label: "关联需求",
            key: "jobPublish",
            children: <Table rowKey="id" columns={tableColumns} dataSource={jobPublishTableData} pagination={false} />,
        },
        {
            label: "职位信息",
            key: "jobInfo",
            children: <JobInfo />,
        },
        {
            label: "流程信息",
            key: "stageInfo",
            children: <RecruitmentProcess flowList={flowDetail?.stage} readonly={true} />,
        },
        {
            label: "历史信息",
            key: "flowInfo",
            children: <FlowInfo />,
        },
    ];

    return (
        <Layout className={styles["job-detail-wrapper"]}>
            <Header title="职位详情" />
            <Layout.Content className={styles["job-detail-content"]}>
                <Flex justify="space-between">
                    <Flex vertical gap={12}>
                        <div className={styles["job-name"]}>
                            {getJobInnerName}-{jobInfo?.jobNameOuter}
                        </div>
                        <Flex align="center" className={styles["job-info"]}>
                            <div>
                                <span>需求部门：</span>
                                <span>{jobInfo?.orgName}</span>
                            </div>
                            <Divider type="vertical" />
                            <div>
                                <span>招聘负责人：</span>
                                <span>
                                    {jobInfo?.createUser?.empId
                                        ? `${jobInfo?.createUser?.empId}-${jobInfo?.createUser?.empName}`
                                        : ""}
                                </span>
                            </div>
                            <Divider type="vertical" />
                            <div>
                                <span>创建时间：</span>
                                <span>{jobInfo?.createTime}</span>
                            </div>
                        </Flex>
                    </Flex>
                    <Flex>
                        <Button color="primary" variant="outlined" onClick={handleEditJob}>
                            编辑职位
                        </Button>
                    </Flex>
                </Flex>
                <InterviewStage
                    statusList={statusList}
                    currentStage={currentStage}
                    handleStatusClick={(item) => {
                        setCurrentStage(item);
                        navigate(Path.Candidate);
                    }}
                />
                <Tabs items={tabsItems} />
            </Layout.Content>
        </Layout>
    );
};

export default JobDetail;
