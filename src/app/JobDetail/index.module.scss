.job-detail-wrapper {
    display: flex;
    flex-direction: column;

    .job-detail-content {
        display: flex;
        flex-direction: column;

        .job-name {
            font-size: 20px;
            font-weight: 600;
        }

        .job-info {
            color: var(--sub-text-color);
        }

        .flow-job-detail-content {
            .title {
                color: var(--sub-text-color);
            }
            .value {
                color: var(--main-text-color);
            }
            .flow-job-detail-basic {
                display: grid;
                grid-template-columns: repeat(4, minmax(0, 1fr));
                gap: 20px;
            }
            .jd-content {
                white-space: pre-wrap;
                margin-bottom: 12px;
            }
        }
    }

    :global {
        .ant-divider-vertical {
            border-inline-start-color: var(--sub-text-color-2);
        }
        .ant-tabs {
            overflow: hidden;
            .ant-tabs-content-holder {
                overflow: auto;
                padding: 16px;
            }
        }
    }
}

.job-edit-wrapper {
    .form-title {
        font-weight: 600;
    }
    .form-content {
        padding: 0 12px;
    }
    :global {
        .ant-collapse-header {
            border-bottom: 1px solid var(--border-color);
        }
        .ant-layout-content {
            overflow: auto;
        }
    }
}
