import React, { useState, useRef, useEffect } from "react";
import { Spin, Empty, Button, Dropdown, Divider } from "antd";
import { CaretDownOutlined } from "@ant-design/icons";
import ZoomIcon from "@/app/icons/talentTool/zoom.svg";
import ZoomOutIcon from "@/app/icons/talentTool/zoomout.svg";
import TalentCard from "./TalentCard";
import Pagination from "./Pagination";
import FilterBar, { type FilterOption, type SortOption } from "../../../components/FilterBar";
import { type FilterState } from "../../../hooks/useFilterState";
import { PaginationInfo, type DictData, type RespParams } from "@/app/typing";
import { getDictDataByCodeApi } from "@/app/request/modules/common";
import styles from "./index.module.scss";
import { CandidateListResp } from "@/app/store/modules/candidate";

/** 人才数据接口 */
interface TalentInfo {
    id: string;
    name: string;
    gender: string;
    position: string;
    company: string;
    skills: string[];
}

/** MainWrapper组件属性接口 */
interface MainWrapperProps {
    /** 人才列表数据 */
    talentList?: CandidateListResp[];
    /** 加载状态 */
    loading?: boolean;
    /** 筛选条件 */
    filters?: FilterState;
    /** 分页信息 */
    pagination?: PaginationInfo;
    /** 搜索回调 */
    onSearch?: (keyword: string) => void;
    /** 筛选回调 */
    onFilterChange?: (filters: Partial<FilterState>) => void;
    /** 重置筛选回调 */
    onResetFilters?: () => void;
    /** 分页回调 */
    onPageChange?: (page: number, pageSize: number) => void;
    /** 人才卡片点击回调 */
    onTalentClick?: (talent: CandidateListResp) => void;
    /** 是否有筛选条件 */
    hasFilters?: boolean;
}

/**
 * 主内容区域组件
 * @param props MainWrapper组件属性
 * @returns MainWrapper组件
 */
const MainWrapper: React.FC<MainWrapperProps> = ({
    talentList = [],
    loading = false,
    filters = {},
    pagination = { pageNum: 1, pageSize: 20, total: 300 },
    onSearch,
    onFilterChange,
    onResetFilters,
    onPageChange,
    onTalentClick,
    hasFilters = false,
}) => {
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [educationList, setEducationList] = useState<{ label: string; value: string }[]>([]);
    const [filterOptions, setFilterOptions] = useState<FilterOption[]>([]);
    const [sortOptions, setSortOptions] = useState<SortOption[]>([]);
    const mainContentRef = useRef<HTMLDivElement>(null);

    // 定义筛选条件配置
    const initializeFilterOptions = () => {
        const allFilterOptions: FilterOption[] = [
            {
                key: "education",
                label: "学历",
                type: "select",
                enabled: true,
                width: 120,
                options: educationList,
            },
            {
                key: "workExperience",
                label: "工作年限",
                type: "select",
                enabled: true,
                width: 130,
                options: [
                    { value: "1年以下", label: "1年以下" },
                    { value: "1-3年", label: "1-3年" },
                    { value: "3-5年", label: "3-5年" },
                    { value: "5-10年", label: "5-10年" },
                    { value: "10年以上", label: "10年以上" },
                ],
            },
            {
                key: "company",
                label: "公司",
                type: "select",
                enabled: true,
                width: 120,
                options: [
                    { value: "字节跳动", label: "字节跳动" },
                    { value: "阿里巴巴", label: "阿里巴巴" },
                    { value: "腾讯", label: "腾讯" },
                    { value: "百度", label: "百度" },
                ],
            },
            {
                key: "position",
                label: "任职职位",
                type: "select",
                enabled: true,
                width: 130,
                options: [
                    { value: "前端工程师", label: "前端工程师" },
                    { value: "后端工程师", label: "后端工程师" },
                    { value: "算法工程师", label: "算法工程师" },
                    { value: "产品经理", label: "产品经理" },
                ],
            },
            {
                key: "school",
                label: "学校",
                type: "select",
                enabled: true,
                width: 120,
                options: [
                    { value: "清华大学", label: "清华大学" },
                    { value: "北京大学", label: "北京大学" },
                    { value: "复旦大学", label: "复旦大学" },
                    { value: "上海交通大学", label: "上海交通大学" },
                ],
            },
            {
                key: "skills",
                label: "技能标签",
                type: "multiple",
                enabled: true,
                width: 180,
                options: [
                    { value: "JavaScript", label: "JavaScript" },
                    { value: "React", label: "React" },
                    { value: "Vue", label: "Vue" },
                    { value: "Node.js", label: "Node.js" },
                    { value: "Python", label: "Python" },
                    { value: "Java", label: "Java" },
                ],
            },
            {
                key: "graduationDate",
                label: "毕业时间",
                type: "date",
                enabled: true,
                width: 200,
            },
        ];
        setFilterOptions(allFilterOptions);
    };

    // 初始化排序选项
    const initializeSortOptions = () => {
        const defaultSortOptions: SortOption[] = [
            { key: "name", label: "姓名", direction: null },
            { key: "age", label: "年龄", direction: null },
            { key: "createTime", label: "创建时间", direction: null },
        ];
        setSortOptions(defaultSortOptions);
    };

    // 获取学历数据
    const getEducationList = async () => {
        try {
            const res: RespParams<DictData[]> = await (await getDictDataByCodeApi("Education")).json();
            if (res.code === 200) {
                const educationOptions = (res?.data ?? []).map((item: DictData) => ({
                    label: item.key,
                    value: item.value,
                }));
                setEducationList(educationOptions);
            }
        } catch (error) {
            console.error("获取学历数据失败:", error);
        }
    };

    useEffect(() => {
        getEducationList();
        initializeSortOptions();
    }, []);

    useEffect(() => {
        initializeFilterOptions();
    }, [educationList]);

    /**
     * 处理筛选条件配置变化
     */
    const handleFilterOptionsChange = (newFilterOptions: FilterOption[]) => {
        setFilterOptions(newFilterOptions);
    };

    /**
     * 处理排序变化
     */
    const handleSortChange = (newSortOptions: SortOption[]) => {
        setSortOptions(newSortOptions);
        // TODO: 实现排序逻辑
    };

    /**
     * 处理分页变化
     * @param page 页码
     * @param pageSize 每页大小
     */
    const handlePageChange = (page: number, pageSize: number) => {
        onPageChange?.(page, pageSize);
    };

    /**
     * 处理分页大小变化
     * @param pageSize 每页大小
     */
    const handlePageSizeChange = (pageSize: number) => {
        handlePageChange(1, pageSize);
    };

    /**
     * 处理人才卡片点击
     * @param talent 人才信息
     */
    const handleTalentClick = (talent: CandidateListResp) => {
        onTalentClick?.(talent);
    };

    /**
     * 处理全屏切换
     */
    const handleFullscreenToggle = async () => {
        if (!mainContentRef.current) return;

        try {
            if (!document.fullscreenElement) {
                // 进入全屏
                await mainContentRef.current.requestFullscreen();
                setIsFullscreen(true);
            } else {
                // 退出全屏
                await document.exitFullscreen();
                setIsFullscreen(false);
            }
        } catch (error) {
            console.error("全屏操作失败:", error);
        }
    };

    // 监听全屏状态变化
    useEffect(() => {
        const handleFullscreenChange = () => {
            setIsFullscreen(!!document.fullscreenElement);
        };

        document.addEventListener("fullscreenchange", handleFullscreenChange);
        return () => {
            document.removeEventListener("fullscreenchange", handleFullscreenChange);
        };
    }, []);

    return (
        <div
            ref={mainContentRef}
            className={`${styles["main-content"]} ${isFullscreen ? styles["main-content--fullscreen"] : ""}`}
        >
            <div className={styles["main-wrapper__header"]}>
                <div className={styles["header-left"]}>
                    <div className={styles["header-title-section"]}>
                        <span className={styles["main-wrapper__header__title"]}>人才推荐</span>
                    </div>
                    <Divider type="vertical" />
                    <div className={styles["header-position-section"]}>
                        <Dropdown
                            menu={{
                                items: [
                                    { key: "1", label: "算法工程师" },
                                    { key: "2", label: "前端工程师" },
                                    { key: "3", label: "后端工程师" },
                                    { key: "4", label: "产品经理" },
                                ],
                            }}
                            trigger={["click"]}
                        >
                            <div className={styles["position-selector"]}>
                                <span>算法工程师</span>
                                <CaretDownOutlined className={styles["dropdown-icon"]} />
                            </div>
                        </Dropdown>
                    </div>
                </div>
                <div className={styles["header-right"]}>
                    <Button
                        type="text"
                        icon={isFullscreen ? <ZoomOutIcon /> : <ZoomIcon />}
                        className={styles["more-button"]}
                        onClick={handleFullscreenToggle}
                        title={isFullscreen ? "退出全屏" : "进入全屏"}
                    />
                </div>
            </div>

            {/* 筛选栏 */}
            <FilterBar
                filters={filters}
                onSearch={onSearch}
                onFilterChange={onFilterChange}
                onResetFilters={onResetFilters}
                hasFilters={hasFilters}
                filterOptions={filterOptions}
                onFilterOptionsChange={handleFilterOptionsChange}
                sortOptions={sortOptions}
                onSortChange={handleSortChange}
            />

            {/* 人才列表 */}
            <div className={styles["main-content__list"]}>
                {loading ? (
                    <div className={styles["main-content__loading"]}>
                        <Spin size="large" />
                    </div>
                ) : talentList.length > 0 ? (
                    talentList.map((talent) => (
                        <TalentCard
                            key={talent.profile.applicantId}
                            talent={talent}
                            onClick={() => handleTalentClick(talent)}
                        />
                    ))
                ) : (
                    <div className={styles["main-content__empty"]}>
                        <Empty description="暂无人才数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    </div>
                )}
            </div>

            {/* 分页 */}
            <div className={styles["pagination-wrapper"]}>
                <Pagination
                    current={pagination.pageNum}
                    pageSize={pagination.pageSize}
                    total={pagination?.total ?? 10}
                    showSizeChanger
                    showQuickJumper={false}
                    showTotal
                    onChange={handlePageChange}
                    onShowSizeChange={handlePageSizeChange}
                />
            </div>
        </div>
    );
};

export default MainWrapper;
export type { TalentInfo, PaginationInfo, MainWrapperProps };
