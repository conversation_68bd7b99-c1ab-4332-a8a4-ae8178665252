.main-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #fff;
    transition: all 0.3s ease;

    &--fullscreen {
        background: white;
        padding: 20px;

        // 全屏时的特殊样式
        .main-wrapper__header {
            margin-bottom: 20px;
        }

        .main-content__list {
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }
    }

    .main-wrapper__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding: 0 4px;

        .main-wrapper__header__title {
            color: var(--main-text-color);
            font-weight: 600;
            font-size: 18px;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 4px;

            .title {
                font-size: 20px;
                font-weight: 600;
                color: #1a1a1a;
                margin: 0;
            }

            .position-selector {
                color: var(--main-text-color);
                font-weight: 600;
                font-size: 18px;
                gap: 4px;
                display: flex;
                align-items: center;

                padding: 6px 4px;
                border-radius: 4px;
                &:hover {
                    background-color: #e8e8e8;
                }
            }
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;

            .ant-btn {
                border: none;
                box-shadow: none;
                background: transparent;

                &:hover {
                    background-color: #f5f5f5;
                }
            }

            .more-button {
                // opacity: 0;
            }
        }
    }

    .filter-section {
        .filter-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            .filter-left {
                display: flex;
                align-items: center;
                gap: 8px;

                .filter-button {
                    background: #e6f5fe;
                    border-color: #e6f5fe;
                    color: #202223;
                    font-size: 500;
                    border-radius: 6px;
                    height: 32px;
                    padding: 0 12px;
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .filter-count {
                        display: inline-block;
                        background: #2f99f2;
                        border-radius: 4px;
                        padding: 0 6px;
                        font-size: 12px;
                        width: 18px;
                        height: 18px;
                        line-height: 18px;
                        color: #fff;
                    }
                }

                .collapse-button {
                    border: none;
                    box-shadow: none;
                    background: #f5f5f5;
                    border-radius: 6px;
                    height: 32px;
                    padding: 0 12px;
                    color: #202223;

                    &:hover {
                        background: #e6f7ff;
                        color: #1890ff;
                    }
                }

                .auto-save-button {
                    border: none;
                    box-shadow: none;
                    background: #f5f5f5;
                    border-radius: 6px;
                    height: 32px;
                    width: 32px;
                    padding: 0;
                    color: #666;

                    &:hover {
                        background: #e6f7ff;
                        color: #1890ff;
                    }
                }
            }

            .filter-right {
                display: flex;
                align-items: center;
                gap: 8px;

                .smart-search {
                    flex: 1;
                    width: 360px;
                    position: relative;
                    background:
                        linear-gradient(white, white) padding-box,
                        linear-gradient(to right, #0099f2, #843dff, #33d1cc) border-box;
                    border: 2px solid transparent;
                    border-radius: 6px;
                }
            }
        }
        .search-row {
            display: flex;
            gap: 8px;
            .filter-item {
                :global(.ant-select-selector) {
                    border: none;
                    background-color: #f5f5f5;
                }
                :global(.ant-select-prefix) {
                    color: rgba(0, 0, 0, 0.42);
                }
            }
            //
            .search-actions {
                display: flex;
                align-items: center;
                gap: 8px;

                .action-button {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 32px;
                    height: 32px;
                    border: 1px solid #d9d9d9;
                    border-radius: 6px;
                    background: white;
                    cursor: pointer;
                    transition: all 0.2s;

                    &:hover {
                        border-color: #1890ff;
                        color: #1890ff;
                    }
                }
            }
        }

        // 筛选表单样式
        .filter-form {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            padding: 16px 0;
        }

        .filter-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-width: 120px;

            &--range {
                min-width: 200px;
            }
        }

        .filter-label {
            font-size: 12px;
            font-weight: 500;
            color: #666666;
            line-height: 1.2;
        }

        .filter-selector {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: #f8f9fa;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            min-height: 36px;

            &:hover {
                border-color: #1890ff;
                background: #f0f8ff;
            }
        }

        .filter-content {
            font-size: 13px;
            color: #333333;
            flex: 1;
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .filter-arrow {
            font-size: 10px;
            color: #999999;
            margin-left: 8px;
            flex-shrink: 0;
        }

        .filter-range {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-range-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: #f8f9fa;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 80px;
            min-height: 32px;

            &:hover {
                border-color: #1890ff;
                background: #f0f8ff;
            }

            .filter-content {
                font-size: 12px;
            }

            .filter-arrow {
                font-size: 8px;
                margin-left: 4px;
            }

            .filter-range-separator {
                font-size: 12px;
                color: #666666;
                margin: 0 4px;
            }

            //
        }
    }

    &__search {
        margin-bottom: 16px;
    }

    &__filter {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;
    }

    &__list {
        flex: 1;
        overflow-y: auto;
        margin-bottom: 16px;
    }

    &__loading {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;
        font-size: 14px;
        color: #666;
    }

    &__empty {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 300px;
    }

    &__pagination {
        display: flex;
        justify-content: center;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
    }
}

.talent-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    @media (min-width: 1200px) {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }
}

.empty-content {
    text-align: center;
    padding: 40px 20px;

    .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.6;
    }

    .empty-text {
        font-size: 16px;
        color: #333;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .empty-desc {
        font-size: 14px;
        color: #999;
        line-height: 1.5;
    }
}

// 响应式布局
@media (max-width: 768px) {
    .main-content {
        padding: 12px;

        &__search {
            margin-bottom: 12px;
        }

        &__filter {
            margin-bottom: 12px;
            padding-bottom: 12px;
        }

        &__list {
            margin-bottom: 12px;
        }

        &__pagination {
            padding-top: 12px;
        }
    }

    .empty-content {
        padding: 30px 15px;

        .empty-icon {
            font-size: 36px;
            margin-bottom: 12px;
        }

        .empty-text {
            font-size: 15px;
            margin-bottom: 6px;
        }

        .empty-desc {
            font-size: 13px;
        }
    }
}

.main-content__list {
    padding-top: 12px;
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
            background: #a8a8a8;
        }
    }
}

.pagination-wrapper {
    width: 100%;
}
