import React, { ReactNode, useEffect, useState } from "react";
import { <PERSON><PERSON>, Divider, Row, Col, Rate, Checkbox, Flex, Tag, Tooltip } from "antd";
import styles from "./index.module.scss";
import Tags from "@/app/components/Tags";
import InfoField, { InfoFieldGroup } from "../../../../components/InfoField";
import AiSuggestionPrefix from "@/app/icons/talentTool/ai-suggestion-prefix.svg";
import EducationExperienceIcon from "@/app/icons/talentTool/education-experience.svg";
import WorkExperienceIcon from "@/app/icons/talentTool/work-experience.svg";
import StarActiveIcon from "@/app/icons/talentTool/star-active.svg";
import UseCandidateStore, { CandidateListResp, TrackStageStatistic } from "@/app/store/modules/candidate";
import { calculateAge, formatTime, copyToClipboard } from "@/app/utils";
import { TextView } from "@/app/components/TextView";

/** 人才信息接口 */
interface TalentInfo {
    id: string;
    name: string;
    position: string;
    company: string;
    skills: string[];
    // status: "active" | "inactive";
    isFavorite?: boolean;
    gender?: string;
}

/** TalentCard组件属性接口 */
interface TalentCardProps {
    /** 人才信息 */
    talent: CandidateListResp;
    /** 是否显示操作按钮 */
    showActions?: boolean;
    /** 卡片点击回调 */
    onClick?: (talent: CandidateListResp) => void;
    /** 收藏切换回调 */
    onFavoriteToggle?: (talentId: string, isFavorite: boolean) => void;
    /** 查看详情回调 */
    onViewDetail?: (talentId: string) => void;
    /** 联系人才回调 */
    onContact?: (talentId: string) => void;
    /** 选中，或取消选中人才卡片 */
    handleCardSelect?: (talent: CandidateListResp) => void;
    isSelect?: boolean;
    disabledSelect?: boolean;
    jobName?: string;
    currentStage?: TrackStageStatistic;
    // 操作按钮自定义
    renderOperationBtn?: ReactNode;
    // 是否淘汰
    isEliminated?: boolean;
}

/**
 * 人才卡片组件
 * @param props TalentCard组件属性
 * @returns TalentCard组件
 */
const TalentCard: React.FC<TalentCardProps> = ({
    talent,
    showActions = true,
    jobName,
    onClick,
    onFavoriteToggle,
    onViewDetail,
    onContact,
    handleCardSelect,
    isSelect = false,
    disabledSelect = false,
    currentStage,
    renderOperationBtn,
    isEliminated = false,
}) => {
    /**
     * 处理卡片点击
     */
    const handleCardClick = () => {
        onClick?.(talent);
    };

    /**
     * 处理收藏切换
     * @param e 点击事件
     */
    const handleFavoriteToggle = (e: React.MouseEvent) => {
        e.stopPropagation();
        onFavoriteToggle?.(talent.profile.applicantId, true);
    };

    /**
     * 处理查看详情
     * @param e 点击事件
     */
    const handleViewDetail = (e: React.MouseEvent) => {
        e.stopPropagation();
        onViewDetail?.(talent.profile.applicantId);
    };

    /**
     * 处理联系人才
     * @param e 点击事件
     */
    const handleContact = (e: React.MouseEvent) => {
        e.stopPropagation();
        onContact?.(talent.profile.applicantId);
    };

    const [checkedSelected, setCheckedSelected] = useState(isSelect);

    useEffect(() => {
        setCheckedSelected(isSelect);
    }, [isSelect]);

    const onSelectChange = () => {
        if (disabledSelect) return;
        setCheckedSelected(!checkedSelected);
        handleCardSelect?.(talent);
    };

    // 提取短信内容中的短链接
    const extractShortUrl = (content: string): string | null => {
        if (!content) return null;

        // 匹配常见的短链接格式，如 http://xxx、https://xxx 等
        // 排除常见的标点符号作为链接结尾
        const urlRegex = /(https?:\/\/[^\s，。！？；：""''（）【】]+)/g;
        const matches = content.match(urlRegex);

        if (matches && matches.length > 0) {
            // 返回第一个找到的链接
            return matches[0];
        }

        return null;
    };

    const renderStatus = () => {
        const sendStatus = talent.application?.smsSendResult?.sendStatus;
        let smsColor = "processing";
        if (sendStatus && /成功/.test(sendStatus)) {
            smsColor = "success";
        } else if (sendStatus && /失败/.test(sendStatus)) {
            smsColor = "error";
        }

        // 渲染短信内容的 Tooltip
        const renderSmsTooltip = () => {
            const smsContent =
                smsColor === "error"
                    ? talent.application.smsSendResult.errMsg
                    : talent.application.smsSendResult.content;

            const shortUrl = extractShortUrl(smsContent);

            return (
                <div style={{ maxWidth: 300 }}>
                    <div style={{ marginBottom: 8 }}>（目前先展示短信内容，后期用户使用需要删除。）</div>
                    <div style={{ marginBottom: shortUrl ? 8 : 0 }}>{smsContent}</div>
                    {shortUrl && (
                        <div
                            style={{
                                padding: "4px 8px",
                                backgroundColor: "#f0f0f0",
                                borderRadius: 4,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                            }}
                        >
                            <span
                                style={{
                                    fontSize: "12px",
                                    color: "#666",
                                    wordBreak: "break-all",
                                    marginRight: 8,
                                }}
                            >
                                {shortUrl}
                            </span>
                            <Button
                                type="link"
                                size="small"
                                style={{ padding: 0, height: "auto", fontSize: "12px" }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    copyToClipboard(shortUrl);
                                }}
                            >
                                复制
                            </Button>
                        </div>
                    )}
                </div>
            );
        };

        const env = process.env.NEXT_PUBLIC_NODE_ENV;

        return (
            <div>
                <Tag color={isEliminated ? "error" : "processing"}>
                    {isEliminated
                        ? "已淘汰"
                        : !currentStage
                          ? "待分配"
                          : `${currentStage?.stageName}(${currentStage?.currentState?.stateName})`}
                </Tag>
                {sendStatus ? (
                    <Tooltip title={renderSmsTooltip()} open={env !== "production" ? undefined : false}>
                        <Tag color={smsColor}>短信{sendStatus}</Tag>
                    </Tooltip>
                ) : (
                    <Tag color="default">未查询到短信状态</Tag>
                )}
            </div>
        );
    };

    return (
        <div className={styles["talent-card"]} onClick={onSelectChange}>
            <Flex align="start" gap={16}>
                <Checkbox
                    style={{ marginTop: 6 }}
                    disabled={disabledSelect}
                    checked={checkedSelected}
                    onChange={() => onSelectChange()}
                ></Checkbox>
                <div style={{ flex: 1 }}>
                    <div className={styles["card-header"]}>
                        <div className={styles["header-left"]}>
                            {/* <StarActiveIcon /> */}
                            <Button
                                style={{ padding: 0 }}
                                className={styles["header-name"]}
                                type="link"
                                onClick={handleCardClick}
                            >
                                {talent?.profile?.name}
                            </Button>
                            <Divider type="vertical" style={{ width: "2px", backgroundColor: "#00000042" }} />
                            <div className={styles["header-base-info"]}>
                                <span className={styles["gender"]}>
                                    {talent?.profile?.gender === "男"
                                        ? "男"
                                        : talent?.profile?.gender === "女"
                                          ? "女"
                                          : "暂无"}
                                </span>
                                <i className={styles["header-base-info-divider"]} />
                                <span className={styles["age"]}>
                                    {talent?.profile?.birth ? `${calculateAge(talent?.profile?.birth)}岁` : "暂无"}
                                </span>
                                <i className={styles["header-base-info-divider"]} />
                                <span className={styles["work-experience"]}>
                                    {talent?.profile?.workExperienceYears ?? 0}年工作经验
                                </span>
                            </div>
                            <Divider type="vertical" style={{ width: "2px", backgroundColor: "#00000042" }} />
                            {/* <Tag color="#fdebd9" style={{ color: "#dc6e01", margin: 0 }}>
                                内推
                            </Tag>
                            <Divider type="vertical" /> */}
                            <div style={{ width: "50%" }}>
                                <Tags
                                    tags={
                                        talent.allTags?.map((item) => ({
                                            name: item.name,
                                            isMatched: item.isMatched ?? true,
                                        })) ?? []
                                    }
                                    color="#465ce8"
                                />
                            </div>
                        </div>
                        <div className={styles["header-right"]}>
                            {/* 更新于：xx */}
                            <span className={styles["header-right-time"]}>更新于：{talent?.profile?.updatedAt}</span>
                            <div className={styles["header-right-operate"]}>
                                {renderOperationBtn}
                                {/* <Button size="small">标记已读</Button>
                                <Button size="small">加入文件夹</Button>
                                <Button size="small">填写备注</Button> */}
                                <Button size="small" onClick={handleCardClick}>
                                    查看详情
                                </Button>
                            </div>
                        </div>
                    </div>
                    <div className={styles["card-content"]}>
                        <div className={styles["card-content-info"]}>
                            <Flex gap={20} justify="space-between">
                                <div className={styles["info-column"]}>
                                    <InfoFieldGroup firstIcon={<WorkExperienceIcon />}>
                                        {talent?.profile?.workExperience.map((item, index) => {
                                            return (
                                                <InfoField
                                                    key={index}
                                                    label={`${formatTime(item.startDate)}-${item.endDate ? formatTime(item.endDate) : "至今"}`}
                                                    labelWidth={165}
                                                    value={
                                                        <div>
                                                            <span>{item.company}</span>
                                                            <Divider
                                                                type="vertical"
                                                                style={{
                                                                    width: "2px",
                                                                    backgroundColor: "#00000042",
                                                                }}
                                                            />
                                                            <span>{item.position}</span>
                                                        </div>
                                                    }
                                                />
                                            );
                                        })}
                                    </InfoFieldGroup>
                                    <InfoFieldGroup firstIcon={<EducationExperienceIcon />}>
                                        {talent?.profile?.educationExperience.map((item, index) => {
                                            return (
                                                <InfoField
                                                    key={index}
                                                    label={`${formatTime(item.startDate)}-${formatTime(item.endDate)}`}
                                                    labelWidth={165}
                                                    value={
                                                        <div>
                                                            <span>{item.school}</span>
                                                            <Divider
                                                                type="vertical"
                                                                style={{
                                                                    width: "2px",
                                                                    backgroundColor: "#00000042",
                                                                }}
                                                            />
                                                            <span>{item.degree}</span>
                                                        </div>
                                                    }
                                                />
                                            );
                                        })}
                                    </InfoFieldGroup>
                                </div>
                                <div className={styles["info-column"]}>
                                    <InfoField label="申请日期" value={talent?.profile?.createdAt} />
                                    <InfoField label="应聘职位" value={jobName} />
                                    <InfoField label="当前状态" value={renderStatus()} />
                                    <InfoField label="最新备注" value={talent.application?.remarks?.[0]} />
                                </div>
                                <div className={styles["info-column"]}>
                                    <InfoField
                                        label="AI评估"
                                        labelWidth={58}
                                        value={
                                            talent.application.portraitAnalysis.talentEstimate
                                            // <div className={styles["evaluate-wrapper"]}>
                                            //     <Tag color="#2ebf47" className={styles["evaluate-grade"]}>
                                            //         <CheckCircleOutlined style={{ marginRight: 4 }} />
                                            //         符合
                                            //     </Tag>
                                            //     4.5分
                                            // </div>
                                        }
                                    />
                                    <InfoField
                                        label="画像匹配"
                                        labelWidth={58}
                                        value={
                                            <div className={styles["rate-wrapper"]}>
                                                <Rate
                                                    allowHalf
                                                    value={talent.application.portraitAnalysis.matchScore}
                                                    disabled
                                                    className={styles["rate"]}
                                                />
                                                {talent.application.portraitAnalysis.matchScore}
                                            </div>
                                        }
                                    />
                                    {/* <InfoField
                                            label="心理风险"
                                            labelWidth={58}
                                            value={<div style={{ fontWeight: 600 }}>高</div>}
                                        /> */}
                                </div>
                            </Flex>
                        </div>
                    </div>
                    <div className={styles["card-footer"]}>
                        <AiSuggestionPrefix className={styles["ai-suggestion-prefix"]} />
                        <div className={styles["ai-suggestion-content"]}>
                            {talent.application.portraitMatching.simplifiedReview}
                        </div>
                    </div>
                </div>
            </Flex>
        </div>
    );
};

export default TalentCard;
export type { TalentInfo, TalentCardProps };
