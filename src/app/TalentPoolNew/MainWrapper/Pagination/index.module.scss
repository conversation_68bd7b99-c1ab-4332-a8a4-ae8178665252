.pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background-color: #fff;

    .pagination {
        .ant-pagination-item {
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            transition: all 0.2s;
            margin: 0 4px;
            min-width: 32px;
            height: 32px;
            line-height: 30px;

            &:hover {
                border-color: #40a9ff;
                transform: translateY(-1px);
            }

            &.ant-pagination-item-active {
                background-color: #1890ff;
                border-color: #1890ff;

                a {
                    color: #fff;
                    font-weight: 500;
                }

                &:hover {
                    background-color: #40a9ff;
                    border-color: #40a9ff;
                }
            }

            a {
                color: #595959;
                font-size: 14px;
                transition: color 0.2s;

                &:hover {
                    color: #1890ff;
                }
            }
        }

        .ant-pagination-jump-prev,
        .ant-pagination-jump-next {
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            margin: 0 4px;
            min-width: 32px;
            height: 32px;
            line-height: 30px;
            transition: all 0.2s;

            &:hover {
                border-color: #40a9ff;
                transform: translateY(-1px);

                .ant-pagination-item-link {
                    color: #1890ff;
                }
            }

            .ant-pagination-item-link {
                color: #595959;
                transition: color 0.2s;
            }
        }

        .ant-pagination-options {
            margin-left: 16px;

            .ant-pagination-options-size-changer {
                .ant-select {
                    .ant-select-selector {
                        border-radius: 6px;
                        border: 1px solid #d9d9d9;
                        transition: all 0.2s;

                        &:hover {
                            border-color: #40a9ff;
                        }
                    }

                    &.ant-select-focused {
                        .ant-select-selector {
                            border-color: #1890ff;
                            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                        }
                    }
                }
            }

            .ant-pagination-options-quick-jumper {
                margin-left: 16px;
                color: #595959;
                font-size: 14px;

                input {
                    border-radius: 6px;
                    border: 1px solid #d9d9d9;
                    transition: all 0.2s;
                    width: 50px;
                    text-align: center;

                    &:hover {
                        border-color: #40a9ff;
                    }

                    &:focus {
                        border-color: #1890ff;
                        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                    }
                }
            }
        }
    }
}

.pagination-prev,
.pagination-next {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0 8px;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    background-color: #fff;
    color: #595959;
    font-size: 14px;
    transition: all 0.2s;
    cursor: pointer;
    min-width: 32px;
    height: 32px;
    justify-content: center;

    &:hover {
        border-color: #40a9ff;
        color: #1890ff;
        transform: translateY(-1px);
    }

    &.ant-pagination-disabled {
        color: #bfbfbf;
        border-color: #f0f0f0;
        background-color: #f5f5f5;
        cursor: not-allowed;

        &:hover {
            transform: none;
            border-color: #f0f0f0;
            color: #bfbfbf;
        }
    }
}

.pagination-prev-text,
.pagination-next-text {
    font-size: 13px;
}

.pagination-total {
    color: #252525;
    font-size: 14px;
}

// 响应式设计
@media (max-width: 768px) {
    .pagination-wrapper {
        padding: 12px 0;
        flex-direction: column;
        gap: 12px;

        .pagination {
            .ant-pagination-item,
            .ant-pagination-jump-prev,
            .ant-pagination-jump-next {
                min-width: 28px;
                height: 28px;
                line-height: 26px;
                margin: 0 2px;

                a {
                    font-size: 13px;
                }
            }

            .ant-pagination-options {
                margin-left: 8px;
                display: flex;
                flex-direction: column;
                gap: 8px;
                align-items: center;

                .ant-pagination-options-quick-jumper {
                    margin-left: 0;
                    font-size: 13px;

                    input {
                        width: 40px;
                    }
                }
            }
        }
    }

    .pagination-prev,
    .pagination-next {
        min-width: 28px;
        height: 28px;
        padding: 0 6px;

        .pagination-prev-text,
        .pagination-next-text {
            font-size: 12px;
        }
    }

    .pagination-total {
        font-size: 13px;
        margin-right: 0;
        margin-bottom: 8px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .pagination-wrapper {
        .pagination {
            .ant-pagination-item,
            .ant-pagination-jump-prev,
            .ant-pagination-jump-next {
                min-width: 24px;
                height: 24px;
                line-height: 22px;
                margin: 0 1px;

                a {
                    font-size: 12px;
                }
            }

            .ant-pagination-options {
                .ant-pagination-options-size-changer {
                    .ant-select {
                        font-size: 12px;
                    }
                }

                .ant-pagination-options-quick-jumper {
                    font-size: 12px;

                    input {
                        width: 35px;
                        font-size: 12px;
                    }
                }
            }
        }
    }

    .pagination-prev,
    .pagination-next {
        min-width: 24px;
        height: 24px;
        padding: 0 4px;

        .pagination-prev-text,
        .pagination-next-text {
            display: none; // 在小屏幕上隐藏文字，只显示图标
        }
    }

    .pagination-total {
        font-size: 12px;
    }
}

// 加载状态
.pagination-wrapper {
    &.loading {
        pointer-events: none;
        opacity: 0.6;
    }
}

// 动画效果
.pagination-wrapper {
    .pagination {
        .ant-pagination-item {
            &.ant-pagination-item-active {
                animation: activePagePulse 0.3s ease-out;
            }
        }
    }
}

@keyframes activePagePulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}
