.talentPool {
    width: 100%;
    height: 100vh;
    background-color: #f5f5f5;
    overflow: hidden;

    .layout {
        height: 100%;
        background-color: transparent;

        .sider {
            background-color: #fff;
            border-right: 1px solid #f0f0f0;
            z-index: 10;
            position: relative;

            // 自定义Sider样式
            :global(.ant-layout-sider-children) {
                height: 100%;
                overflow: hidden;
            }

            // 隐藏默认的折叠按钮
            :global(.ant-layout-sider-trigger) {
                display: none;
            }
        }

        .content {
            background-color: white;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .talentPool {
        .layout {
            .sider {
                width: 280px !important;
                min-width: 280px !important;
                max-width: 280px !important;
            }

            .content {
            }
        }
    }
}

@media (max-width: 768px) {
    .talentPool {
        .layout {
            .sider {
                width: 260px !important;
                min-width: 260px !important;
                max-width: 260px !important;
                position: fixed;
                left: -260px;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
                box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);

                &.open {
                    left: 0;
                }
            }

            .content {
                margin-left: 0;
            }
        }
    }

    // 移动端遮罩层
    .mobileOverlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;

        &.visible {
            opacity: 1;
            visibility: visible;
        }
    }
}

@media (max-width: 480px) {
    .talentPool {
        .layout {
            .sider {
                width: 240px !important;
                min-width: 240px !important;
                max-width: 240px !important;
                left: -240px;
            }

            .content {
            }
        }
    }
}

// 加载状态
.talentPool {
    &.loading {
        .layout {
            .content {
                opacity: 0.6;
                pointer-events: none;
            }
        }
    }
}

// 动画效果
.talentPool {
    .layout {
        .sider {
            transition: all 0.3s ease;
        }

        .content {
            transition: all 0.3s ease;
        }
    }
}

// 深色主题支持
// @media (prefers-color-scheme: dark) {
//     .talentPool {
//         background-color: #141414;

//         .layout {
//             .sider {
//                 background-color: #1f1f1f;
//                 border-right-color: #303030;
//             }

//             .content {
//                 background-color: #141414;
//             }
//         }
//     }
// }

// // 高对比度模式支持
// @media (prefers-contrast: high) {
//     .talentPool {
//         .layout {
//             .sider {
//                 border-right: 2px solid #000;
//             }
//         }
//     }
// }

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
    .talentPool {
        .layout {
            .sider,
            .content {
                transition: none;
            }
        }
    }

    .mobileOverlay {
        transition: none;
    }
}
