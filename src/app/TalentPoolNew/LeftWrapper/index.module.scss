.leftWrapper {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .left-wrapper__header {
        margin-bottom: 12px;
        .left-wrapper__header__title {
            color: var(--main-text-color);
            font-weight: 600;
            font-size: 18px;
        }

        .left-wrapper__content {
        }
    }

    :global(.ant-tree-treenode-selected) {
        font-weight: 600;
    }

    .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: hidden;

        .divider {
            margin: 10px 0;
            border-color: #f0f0f0;
        }

        .filterSection {
            flex: 1;
            padding: 16px;
            overflow-y: auto;

            // 自定义滚动条样式
            &::-webkit-scrollbar {
                width: 6px;
            }

            &::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;
                transition: background 0.2s;

                &:hover {
                    background: #a8a8a8;
                }
            }
        }

        .folder-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 7px 8px;

            h3 {
                color: rgba(0, 0, 0, 0.6);
                font-size: 12px;
                font-weight: 500;
                line-height: 20px; /* 166.667% */
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .leftWrapper {
        .content {
            .filterSection {
                padding: 12px;
            }
        }
    }
}

@media (max-width: 768px) {
    .leftWrapper {
        border-radius: 0;
        box-shadow: none;
        border-right: 1px solid #f0f0f0;

        .content {
            .filterSection {
                padding: 8px;
            }
        }
    }
}

// 加载状态
.leftWrapper {
    &.loading {
        .content {
            opacity: 0.6;
            pointer-events: none;
        }
    }
}

// 动画效果
.leftWrapper {
    .content {
        .filterSection {
            transition: all 0.3s ease;
        }
    }
}
