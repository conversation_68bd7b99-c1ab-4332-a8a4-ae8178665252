import React, { useEffect, useRef, useState } from "react";
import { Table, Button, Skeleton, Layout } from "antd";
import styles from "./index.module.scss";
import FlowDetail from "./FlowDetail";
import { getFlowListApi } from "@/app/request/modules/dashboard";
import { FlowDetailExposeHandle, FlowHistoryRecord } from "@/app/store/modules/dashboard/jobFlow";
import Header from "@/app/components/Header";
import ChatDrawer from "@/app/components/ChatDrawer";
import { getTableScroll } from "@/app/utils";
import { ColumnsType } from "antd/es/table";

// 工作台流程审批tab key
const TAB_FLOW = "2";

/** 历史审批页面组件 */
export default function FlowHistory() {
    const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
    const [currentRecord, setCurrentRecord] = useState<FlowHistoryRecord | null>(null);

    const handleDetail = (record: FlowHistoryRecord) => {
        setCurrentRecord(record);
        setDrawerVisible(true);
        setTimeout(() => {
            flowDetailRef.current?.refresh();
        }, 100);
    };

    const columns: ColumnsType<FlowHistoryRecord> = [
        {
            title: "流程ID",
            dataIndex: "id",
            key: "id",
            width: 250,
            ellipsis: true,
        },
        {
            title: "流程类型",
            dataIndex: "flowType",
            key: "flowType",
            width: 150,
        },
        {
            title: "发起人",
            dataIndex: "initiator",
            key: "initiator",
            width: 150,
            ellipsis: true,
        },
        {
            title: "发起时间",
            dataIndex: "startedAt",
            key: "startedAt",
            width: 200,
            ellipsis: true,
            sorter: (a: FlowHistoryRecord, b: FlowHistoryRecord) =>
                new Date(a.startedAt).getTime() - new Date(b.startedAt).getTime(),
        },
        {
            title: "状态",
            dataIndex: "statusStr",
            key: "statusStr",
            width: 100,
            // filters: [
            //     { text: "审批中", value: "processing" },
            //     { text: "已完成", value: "completed" },
            //     { text: "已驳回", value: "rejected" },
            // ],
        },
        {
            title: "当前节点",
            dataIndex: "currentNodeName",
            key: "currentNodeName",
            width: 150,
            ellipsis: true,
        },
        {
            title: "当前节点审批人",
            dataIndex: "currentNodeApprover",
            key: "currentNodeApprover",
            width: 150,
            ellipsis: true,
        },
        {
            title: "操作",
            key: "action",
            width: 150,
            fixed: "right",
            render: (_: any, record: FlowHistoryRecord) => (
                <Button type="link" onClick={() => handleDetail(record)}>
                    详情
                </Button>
            ),
        },
    ];
    const [loading, setLoading] = useState<boolean>(false);
    const [flowList, setFlowList] = useState<FlowHistoryRecord[]>([]);
    const [scrollY, setScrollY] = useState<number>();
    const [pagination, setPagination] = useState<{
        current: number;
        pageSize: number;
        total: number;
    }>({ current: 1, pageSize: 10, total: 0 });
    const flowDetailRef = useRef<FlowDetailExposeHandle>(null);

    useEffect(() => {
        setScrollY(getTableScroll());
    }, []);

    useEffect(() => {
        getFlowList(pagination.current, pagination.pageSize);
    }, [pagination.current, pagination.pageSize]);

    // 获取流程列表
    const getFlowList = async (pageNum = 1, pageSize = 10) => {
        try {
            setLoading(true);
            const requestParams = {
                pageNum: pageNum - 1, // 后端从0开始计数
                pageSize,
            };
            const res: any = await (await getFlowListApi(requestParams)).json();

            if (res.code === 200) {
                setLoading(false);
                const formattedData: FlowHistoryRecord[] = res.data?.content.map((item: any) => {
                    const currentNode = item.currentNodeInstance;

                    return {
                        ...item,
                        flowType: item.flowDefinition?.name,
                        currentNodeName: currentNode?.nodeDefinition?.name,
                        currentNodeApprover: currentNode?.approver
                            ? `${currentNode?.approver?.emp_id}-${currentNode?.approver?.emp_name}`
                            : "暂无",
                        initiator: item.initiator ? `${item.initiator?.emp_id}-${item.initiator?.emp_name}` : "暂无",
                    };
                });
                setFlowList(formattedData);
                setPagination({
                    ...pagination,
                    current: pageNum,
                    total: res.data?.totalElements || 0,
                });
            }
        } catch (err) {
            setLoading(false);
            console.log("err", err);
        }
    };

    /** 处理分页变化 */
    const handleTableChange = (page: number, pageSize?: number) => {
        setPagination({
            ...pagination,
            current: page,
            pageSize: pageSize || pagination.pageSize,
        });
    };

    return (
        <Layout>
            <Header title="流程历史" />
            <Layout.Content>
                <Skeleton active loading={loading}>
                    <div className={styles["flow-history-content"]}>
                        <Table
                            columns={columns}
                            dataSource={flowList}
                            rowKey="id"
                            scroll={{ y: scrollY }}
                            pagination={{
                                size: "small",
                                current: pagination.current,
                                pageSize: pagination.pageSize,
                                total: pagination.total,
                                hideOnSinglePage: false,
                                showSizeChanger: true,
                                pageSizeOptions: ["10", "20", "50", "100"],
                                onChange: handleTableChange,
                                onShowSizeChange: (current, size) => handleTableChange(1, size),
                                showTotal: (total) => `共 ${total} 条`,
                            }}
                        />
                    </div>
                </Skeleton>
                <ChatDrawer
                    open={drawerVisible}
                    width="50%"
                    title="审批详情"
                    confirmLoading={false}
                    showFooter={false}
                    onCancel={() => setDrawerVisible(false)}
                    onOk={() => {}}
                >
                    <FlowDetail type="JobFlowHistory" ref={flowDetailRef} flowId={currentRecord?.id ?? ""} />
                </ChatDrawer>
            </Layout.Content>
        </Layout>
    );
}
