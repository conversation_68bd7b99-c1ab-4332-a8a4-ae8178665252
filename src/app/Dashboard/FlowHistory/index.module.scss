.flow-history-wrapper {
    width: 100%;
    height: 100%;
    padding: 16px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    gap: 20px;

    .flow-history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
            color: #1f1f1f;
        }

        nav {
            display: flex;
            align-items: center;
        }
    }
}

.flow-job-detail-wrapper {
    margin-bottom: 24px;
    .flow-job-detail-header {
        font-weight: 600;
        font-size: 16px;
        color: var(--main-text-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .flow-job-detail-content {
        .title {
            color: var(--sub-text-color);
        }
        .value {
            color: var(--main-text-color);
        }

        .flow-job-detail-basic {
            display: grid;
            grid-template-columns: repeat(4, minmax(0, 1fr));
            gap: 20px;
        }

        .jd-content {
            white-space: pre-wrap;
            margin-bottom: 12px;
        }
    }
}
