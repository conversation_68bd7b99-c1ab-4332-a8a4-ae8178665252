import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Flex, Modal } from "antd";
import { useEffect, useMemo, useRef, useState } from "react";
import {
    AddFlowItemExposeHandle,
    JobFlowReq,
    MyFlowResp,
    TagsItem,
    TagsResp,
} from "@/app/store/modules/dashboard/jobFlow";
import { formatHtmlWithLists } from "@/app/utils";
import { cloneDeep } from "lodash-es";
import { RespParams } from "@/app/typing";
import { deprecateJobFlowApi, publishJobFlowApi, updateJobFlowApi } from "@/app/request/modules/dashboard";
import messageService from "@/app/lib/message";
import { assignmentList, hiringBatchData, jobTypeData } from "../constant";
import FlowItem from "../AddFlow/FlowItem";
import { TextView } from "@/app/components/TextView";
import JobTagsComp from "../AddFlow/JobTags";
import styles from "./index.module.scss";
import AddFlowDetailChildren from "../AddFlowDetailChildren";
import { ExclamationCircleFilled } from "@ant-design/icons";

const JobDetail = ({
    detail,
    isAllowEdit,
    mode,
    isDefaultExpandedAll = true,
    handleFormLoading,
    handleChangeFormList,
    refresh,
}: {
    detail: any[];
    isAllowEdit: boolean;
    mode?: string;
    isDefaultExpandedAll?: boolean;
    handleFormLoading: (val: boolean) => void;
    handleChangeFormList?: (formList: JobFlowReq[]) => void;
    refresh?: () => void;
}) => {
    // 表单数据
    const [formList, setFormList] = useState<JobFlowReq[]>([]);
    const [activeKey, setActiveKey] = useState<string[]>([]);
    const [buttonLoading, setButtonLoading] = useState(false);
    const flowItemRef = useRef<AddFlowItemExposeHandle>(null);

    useEffect(() => {
        setFormList(detail);
    }, [detail]);

    useEffect(() => {
        handleChangeFormList?.(formList);
    }, [formList]);

    const collapseItems = useMemo(() => {
        const CollapseHeader = ({ item }: { item: JobFlowReq }) => {
            const handleChangeEditMode = (item: JobFlowReq, e: React.MouseEvent<HTMLElement, MouseEvent>) => {
                e.stopPropagation();

                // 格式化对内岗位与组织为数组格式
                item.jobNameInnerPath = !Array.isArray(item.jobNameInnerPath)
                    ? item.jobNameInnerPath.split("/")
                    : item.jobNameInnerPath;
                item.orgId = !Array.isArray(item.orgId) ? item.orgId.split("/") : item.orgId;
                setFormList((prevList) => {
                    return prevList.map((formItem) => {
                        if (formItem.jobId === item.jobId) {
                            return { ...formItem, editMode: true };
                        }
                        return formItem;
                    });
                });
            };

            const handleCancelEditMode = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
                e.stopPropagation();
                setFormList((prevList) => {
                    return prevList.map((formItem) => {
                        if (formItem.jobId === item.jobId) {
                            return { ...formItem, editMode: false };
                        }
                        return formItem;
                    });
                });
            };

            const handleSaveEditMode = async (item: JobFlowReq, e: React.MouseEvent<HTMLElement, MouseEvent>) => {
                e.stopPropagation();

                try {
                    await flowItemRef.current?.getForm().validateFields();
                    const params: any = flowItemRef.current?.getForm().getFieldsValue();
                    params.jobJD = flowItemRef.current?.richTextRef?.current?.getHTML() ?? "";
                    params.jobJD = formatHtmlWithLists(params.jobJD);
                    params.id = item.id;
                    const newParams = cloneDeep(params); // 备份用于展示页面数据

                    if (Array.isArray(params.orgId)) {
                        params.orgId = params?.orgId?.join("/");
                    }
                    if (Array.isArray(params.jobNameInnerPath)) {
                        params.jobNameInnerPath = params?.jobNameInnerPath?.join("/");
                    }
                    if (Array.isArray(params.educational)) {
                        params.educational = params?.educational?.join("/");
                    }
                    if (Array.isArray(params.jobLocation)) {
                        params.jobLocation = params?.jobLocation?.join("/");
                    }

                    // 校验人才比例之后是否为100%
                    if (Math.abs(params.topTalents + params.excellentTalents + params.regularTalents - 100) > 0.01) {
                        messageService.error(
                            `${params?.orgName ?? "默认组织"}-${params?.jobNameInner ?? "默认岗位"} 人才比例之和必须为100%`
                        );
                        return false;
                    }

                    // 检验职位JD是否符合正则校验
                    const regex1 = /(工作职责|岗位职责|主要职责)[:：]\s*(?=\S)/g;
                    const regex2 = /(职位要求|岗位要求|任职要求)[:：]\s*(?=\S)/g;
                    if (!regex1.test(params.jobJD) || !regex2.test(params.jobJD)) {
                        messageService.error(
                            <Flex vertical align="start">
                                <div>岗位JD格式错误，格式必须为: </div>
                                <div>工作职责/岗位职责/主要职责: </div>
                                <div>xxxxxxx</div>
                                <div>职位要求/岗位要求/任职要求:</div>
                                <div>xxxxxxx</div>
                            </Flex>
                        );
                        return false;
                    }

                    params.tags = [];
                    params.jobTags = item.jobTags;
                    params.jobTags?.forEach((item: TagsItem) => {
                        item?.children?.forEach((item: TagsItem) => {
                            params.tags.push(...(item.value?.map((item2: TagsResp) => item2.id) ?? []));
                        });
                    });
                    delete params.jobTags;
                    newParams.jobTags = item.jobTags;

                    handleFormLoading(true);
                    setButtonLoading(true);

                    if (!item.allocationMethod) {
                        const res: RespParams<any> = await (await updateJobFlowApi([params])).json();
                        if (res.code === 200) {
                            messageService.success("保存成功");
                            setFormList((prevList) => {
                                return prevList.map((formItem) => {
                                    if (formItem.jobId === item.jobId) {
                                        return { ...formItem, editMode: false };
                                    }
                                    return formItem;
                                });
                            });
                            handleFormLoading(false);
                            setButtonLoading(false);
                            setFormList((prev) => {
                                const index = prev.findIndex((item2) => item2.id === item.id);
                                if (index !== -1) {
                                    prev[index] = newParams;
                                }
                                return [...prev];
                            });
                        }
                    } else {
                        params.allocationMethod = params.assignment;
                        const selectedData = item.jobSpecs ?? [];
                        params.specIds = selectedData.map((item) => item.id).join(",");
                        params.proportionValues = {};
                        if (params.allocationMethod === 3) {
                            selectedData.forEach((item) => {
                                if (item.id) {
                                    params.proportionValues[item.id] = item.assignment;
                                }
                            });
                        }
                        const res: RespParams<any> = await (await publishJobFlowApi(params)).json();
                        if (res.code === 200) {
                            messageService.success(res.msg ?? "保存成功");
                            setFormList((prevList) => {
                                return prevList.map((formItem) => {
                                    if (formItem.jobId === item.jobId) {
                                        return {
                                            ...formItem,
                                            editMode: false,
                                        };
                                    }
                                    return formItem;
                                });
                            });
                            handleFormLoading(false);
                            setButtonLoading(false);
                            setFormList((prev) => {
                                const index = prev.findIndex((item2) => item2.id === item.id);
                                if (index !== -1) {
                                    prev[index] = newParams;
                                    prev[index].allocationMethod = item.allocationMethod;
                                    prev[index].jobSpecs = item?.jobSpecs;
                                }
                                return [...prev];
                            });
                        }
                    }
                } catch (error) {
                    console.error(error);
                    handleFormLoading(false);
                    setButtonLoading(false);
                }
            };

            const handleReject = async (item: JobFlowReq, e: React.MouseEvent<HTMLElement, MouseEvent>) => {
                e.stopPropagation();
                const result = await new Promise((resolve) => {
                    Modal.confirm({
                        title: "是否废弃该需求?",
                        icon: <ExclamationCircleFilled />,
                        content: "点击确定后废弃，关闭弹窗",
                        onOk() {
                            return new Promise((resolve2, reject2) => {
                                deprecateJobFlowApi({
                                    type: 1,
                                    ids: [item.id ?? 0],
                                })
                                    .then((response) => response.json())
                                    .then((res: any) => {
                                        if (res.code === 200) {
                                            messageService.success("审批废弃");
                                            resolve2(true);
                                            resolve(true);
                                        } else {
                                            reject2(false);
                                        }
                                    })
                                    .catch((err) => {
                                        reject2(false);
                                    });
                            });
                        },
                        onCancel() {
                            resolve(false);
                        },
                    });
                });
                if (result) {
                    refresh?.();
                }
            };

            return (
                <Flex align="center" justify="space-between" key={item.id} className={styles["flow-job-detail-header"]}>
                    {item.orgName}-{item.jobNameInner}
                    <Flex>
                        {formList.length > 1 && (
                            <Button
                                loading={buttonLoading}
                                color="danger"
                                variant="link"
                                onClick={(e) => handleReject(item, e)}
                            >
                                废弃
                            </Button>
                        )}
                        {isAllowEdit &&
                            (!item.editMode ? (
                                <Button color="primary" variant="link" onClick={(e) => handleChangeEditMode(item, e)}>
                                    编辑
                                </Button>
                            ) : (
                                <Flex>
                                    <Button
                                        loading={buttonLoading}
                                        color="default"
                                        variant="link"
                                        onClick={(e) => handleCancelEditMode(e)}
                                    >
                                        取消
                                    </Button>
                                    <Button
                                        loading={buttonLoading}
                                        color="green"
                                        variant="link"
                                        onClick={(e) => handleSaveEditMode(item, e)}
                                    >
                                        保存
                                    </Button>
                                </Flex>
                            ))}
                    </Flex>
                </Flex>
            );
        };

        if (isDefaultExpandedAll) {
            setActiveKey(formList.map((item) => item.jobId));
        } else {
            setActiveKey([]);
        }

        return formList.map((item) => {
            const basicInfo = [
                { title: "职位名称", value: item.jobNameInner },
                { title: "对外职位名称", value: item.jobNameOuter },
                { title: "需求部门", value: item.orgName },
                { title: "招聘批次", value: hiringBatchData.find((item2) => item2.value === item.hiringBatch)?.label },
                { title: "目标人数", value: item.hiringPersonNum },
                { title: "offer目标", value: item.offerTarget },
                { title: "职位类别", value: jobTypeData.find((item2) => item2.value === item.jobType)?.label },
                { title: "原因说明", value: item.reason },
                { title: "拔尖人才(%)", value: item.topTalents?.toFixed(2) },
                { title: "优秀人才(%)", value: item.excellentTalents?.toFixed(2) },
                { title: "普通人才(%)", value: item.regularTalents?.toFixed(2) },
                { title: "工作地点", value: item.jobLocation },
                { title: "学历要求", value: item.educational },
                { title: "语言要求", value: item.language },
            ];

            if (item?.allocationMethod) {
                basicInfo.push({
                    title: "分配方式",
                    value: assignmentList.find((item2) => item2.value === item.allocationMethod)?.label,
                });
            }

            return {
                key: item.jobId,
                label: <CollapseHeader item={item} />,
                children: item.editMode ? (
                    <FlowItem
                        ref={flowItemRef}
                        item={item}
                        mode={mode ?? "edit"}
                        allocationMethod={item.allocationMethod}
                        handleChangeLoading={(val) => {
                            setButtonLoading(val);
                            handleFormLoading(val);
                        }}
                        customChildren={
                            item.allocationMethod && (
                                <AddFlowDetailChildren
                                    tableData={item.jobSpecs as MyFlowResp[]}
                                    isEdit={true}
                                    allocationMethod={item.allocationMethod ?? 0}
                                />
                            )
                        }
                        handleJobNameChange={(val) => {
                            setFormList((prevList) => {
                                return prevList.map((formItem) => {
                                    if (formItem.jobId === item.jobId) {
                                        return { ...formItem, jobNameInner: val };
                                    }
                                    return formItem;
                                });
                            });
                        }}
                        handleOrgChange={(val) => {
                            setFormList((prevList) => {
                                return prevList.map((formItem) => {
                                    if (formItem.jobId === item.jobId) {
                                        return { ...formItem, orgName: val };
                                    }
                                    return formItem;
                                });
                            });
                        }}
                        handleAssignmentChange={(val) => {
                            item.assignment = val ?? 1;
                            setFormList((prev) => {
                                prev.forEach((item2) => {
                                    if (item2.id === item.id) {
                                        item2.allocationMethod = val ?? 1;
                                    }
                                });
                                return [...prev];
                            });
                        }}
                    />
                ) : (
                    <div className={styles["flow-job-detail-content"]}>
                        <div className={styles["flow-job-detail-basic"]}>
                            {basicInfo.map((item, index) => (
                                <div key={index} className={styles["flow-job-detail-basic-item"]}>
                                    <div className={styles["title"]}>{item.title}</div>
                                    <div className={styles["value"]}>
                                        <TextView text={(item?.value ?? "").toString()} />
                                    </div>
                                </div>
                            ))}
                        </div>
                        <div style={{ margin: "12px 0" }}>
                            {item.jobSpecs && item.jobSpecs.length > 0 && (
                                <AddFlowDetailChildren
                                    tableData={item.jobSpecs as MyFlowResp[]}
                                    isEdit={false}
                                    allocationMethod={item.allocationMethod ?? 0}
                                />
                            )}
                        </div>
                        <div className={styles["flow-job-detail-job-jd"]}>
                            <div className={styles["title"]}>岗位JD描述</div>
                            <div className={`${styles["value"]} ${styles["jd-content"]}`}>{item.jobJD}</div>
                        </div>
                        {item.jobTags && (
                            <JobTagsComp tagsList={item.jobTags} editable={false} handleChangeTags={() => {}} />
                        )}
                    </div>
                ),
            };
        });
    }, [formList, buttonLoading]);

    return (
        <div className={styles["flow-job-detail-wrapper"]}>
            <Collapse
                activeKey={activeKey}
                items={collapseItems}
                bordered={false}
                onChange={(val) => setActiveKey(val)}
            />
        </div>
    );
};

export default JobDetail;
