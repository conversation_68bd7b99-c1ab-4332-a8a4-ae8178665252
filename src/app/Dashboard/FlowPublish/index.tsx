import { Button, Flex, Layout, Modal, Spin, Table, Tag, Radio } from "antd";
import { ExclamationCircleFilled } from "@ant-design/icons";
import Header from "@/app/components/Header";
import ProTable, { ActionType, ProColumnType } from "@ant-design/pro-table";
import { useEffect, useRef, useState } from "react";
import useJobFlowStore from "@/app/store/modules/dashboard/jobFlow";
import {
    deprecateJobFlowApi,
    exportDataApi,
    getJobFlowAggregationApi,
    getJobFlowChildrenByIdApi,
    getMyFlowListApi,
    publishJobFlowApi,
} from "@/app/request/modules/dashboard";
import { RespPaginationParams, RespParams } from "@/app/typing";
import {
    AddFlowItemExposeHandle,
    Department,
    FlowDetailExposeHandle,
    FlowItemParams,
    FlowTableExposeHandle,
    Job,
    JobFlowAggregationResp,
    MyFlowReq,
    MyFlowResp,
    PublishMyFlowReq,
    TagsItem,
    TagsResp,
} from "@/app/store/modules/dashboard/jobFlow";
import messageService from "@/app/lib/message";
import ChatDialog from "../../../components/ChatDialog";
import FlowItem from "../AddFlow/FlowItem";
import { defaultFlowItem, JobTags, statusList } from "../constant";
import { nanoid } from "nanoid";
import { arrayToTree, formatHtmlWithLists, getNodeWithAncestors, getTableScroll, treeToArray } from "@/app/utils";
import FlowTable from "./FlowTable";
import { FormInstance } from "antd/lib";
import AddFlowDetailChildren from "../AddFlowDetailChildren";
import FlowDetail from "../FlowHistory/FlowDetail";
import { getDepartmentListApi, getJobListApi } from "@/app/request/modules/common";

const FlowPublish = () => {
    const { departmentList, setDepartmentList } = useJobFlowStore((state) => ({
        departmentList: state.departmentList,
        setDepartmentList: state.setDepartmentList,
    }));

    const [scrollY, setScrollY] = useState<number>();
    const [showPublic, setShowPublic] = useState(false);
    const [showFlowTable, setShowFlowTable] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [assignment, setAssignment] = useState<number>(1);
    const [jobNameInnerPath, setJobNameInnerPath] = useState<string>("");
    const [selectedData, setSelectedData] = useState<MyFlowResp[]>([]);
    const [flows, setFlows] = useState<FlowItemParams | null>(null);
    const [expandedTableData, setExpandedTableData] = useState<Record<string, any[]>>({});
    const [expandedRowKeys, setExpandedKeys] = useState<number[]>([]);
    const [expandedLoading, setExpandedLoading] = useState<boolean>(false);
    const [showJobDetail, setShowJobDetail] = useState<boolean>(false);
    const flowItemRef = useRef<AddFlowItemExposeHandle>(null);
    const tableRef = useRef<ActionType>(null);
    const formRef = useRef<FormInstance>();
    const flowTableRef = useRef<FlowTableExposeHandle>(null);
    const [detailLoading, setDetailLoading] = useState<boolean>(false);
    const [allJobList, setAllJobList] = useState<Job[]>([]);
    const [currentJobList, setCurrentJobList] = useState<Job[]>([]);
    const [currentFlowId, setCurrentFlowId] = useState<string>("");
    const [showSelectType, setShowSelectType] = useState<boolean>(false);
    const [selectedProcessType, setSelectedProcessType] = useState<string>("merge");
    const [showSelectTypeLoading, setShowSelectTypeLoading] = useState<boolean>(false);
    const [currentJobId, setCurrentJobId] = useState<number>();
    const [currentJobType, setCurrentJobType] = useState<string>("");

    const flowDetailRef = useRef<FlowDetailExposeHandle>(null);

    const getDepartmentList = async () => {
        const res: RespParams<Department> = await (await getDepartmentListApi()).json();
        if (res.code === 200) {
            const origin = [res.data].map((item) => {
                if (item.dept_code === "100000") {
                    item.parent_dept_code = "-1";
                }
                return item;
            });
            const list = treeToArray(origin);
            list.forEach((item) => {
                item.isDisabled = item.dept_level === 90;
            });
            const treeData = arrayToTree(list, "dept_code", "parent_dept_code");
            setDepartmentList(treeData);
        }
    };

    const columns: ProColumnType<MyFlowResp>[] = [
        {
            title: "需求部门",
            dataIndex: "orgName",
            width: 300,
            ellipsis: true,
            valueType: "cascader",
            fieldProps: {
                expandTrigger: "hover",
                placeholder: "请选择需求部门",
                showSearch: {
                    limit: Infinity,
                },
                options: departmentList,
                changeOnSelect: true,
                fieldNames: {
                    label: "dept_name",
                    value: "dept_code",
                },
                optionRender: (option: Department) => {
                    return (
                        <div
                            onClick={(e) => {
                                // 禁用节点，禁止点击后选中
                                if (option.isDisabled) {
                                    e.stopPropagation();
                                }
                            }}
                            style={{
                                color: option.isDisabled ? "var(--sub-text-color-2)" : "inherit",
                                cursor: option.isDisabled ? "not-allowed" : "pointer",
                            }}
                        >
                            {option.dept_name}
                        </div>
                    );
                },
                onBlur: async () => {
                    const value = formRef.current?.getFieldValue("orgName");
                    if (value && value.length > 0) {
                        const code = value?.[value?.length - 1];
                        const res: RespParams<Job[]> = await (await getJobListApi({ orgNo: code })).json();
                        if (res.code === 200) {
                            setAllJobList(res.data);
                        }
                    }
                },
                onChange: (value) => {
                    if (!value) {
                        setAllJobList([]);
                        formRef.current?.setFieldsValue({ jobNameInner: undefined });
                    }
                },
            },
        },
        {
            title: "职位名称",
            dataIndex: "jobNameInner",
            width: 300,
            ellipsis: true,
            valueType: "cascader",
            fieldProps: {
                expandTrigger: "hover",
                placeholder: "请选择职位名称",
                showSearch: {
                    limit: Infinity,
                },
                options: allJobList,
                changeOnSelect: true,
                fieldNames: {
                    label: "desc",
                    value: "code",
                },
                onChange: (val: string[], options: Job[]) => {
                    setCurrentJobList(options);
                },
            },
        },
        {
            title: "对外职位名称",
            dataIndex: "jobNameOuter",
            width: 300,
            ellipsis: true,
            valueType: "textarea",
            formItemProps: {
                labelCol: { span: 8 },
            },
            fieldProps: {
                placeholder: "请输入对外职位名称",
            },
        },
        {
            title: "招聘批次",
            dataIndex: "hiringBatch",
            search: false,
            width: 120,
        },
        {
            title: "状态",
            dataIndex: "status",
            valueType: "select",
            width: 120,
            render: (_, record) => {
                const obj = statusList.find((item) => item.value === record.status);

                return <Tag color={obj?.type}>{obj?.label}</Tag>;
            },
            fieldProps: {
                placeholder: "请选择状态",
                options: [...statusList],
            },
        },
        {
            title: "招聘岗位数",
            dataIndex: "specIds",
            search: false,
            width: 120,
            render: (_, record) => {
                return record.specIds?.split(",")?.length;
            },
        },
        {
            title: "需求人数",
            dataIndex: "hiringPersonNum",
            search: false,
            width: 120,
        },
        {
            title: "待入职",
            dataIndex: "title2",
            search: false,
            width: 120,
        },
        {
            title: "已入职",
            dataIndex: "title3",
            search: false,
            width: 120,
        },
        {
            title: "完成率",
            dataIndex: "title4",
            search: false,
            width: 120,
        },
        {
            title: "需求负责人",
            dataIndex: "createUser",
            search: false,
            width: 150,
            render: (_, record) => {
                return record.createUser?.empId + "-" + record.createUser?.empName;
            },
        },
        {
            title: "创建时间",
            dataIndex: "createTime",
            search: false,
            ellipsis: true,
            width: 200,
        },
        {
            title: "修改时间",
            dataIndex: "updateTime",
            search: false,
            ellipsis: true,
            width: 200,
        },
        {
            title: "操作",
            dataIndex: "operation",
            fixed: "right",
            search: false,
            width: 150,
            render: (_, record) => {
                return (
                    <Flex>
                        <Button
                            type="link"
                            onClick={() => {
                                setCurrentJobType("editParentJob");
                                setCurrentJobId(record.id);
                                handleDetail(record.flowInstanceId);
                            }}
                        >
                            详情
                        </Button>
                        <Button
                            color="danger"
                            variant="link"
                            onClick={async () => {
                                const result = await new Promise((resolve) => {
                                    Modal.confirm({
                                        title: "是否废弃该需求?",
                                        icon: <ExclamationCircleFilled />,
                                        content: "点击确定后废弃，关闭弹窗",
                                        onOk() {
                                            return new Promise((resolve2, reject2) => {
                                                deprecateJobFlowApi({
                                                    type: 2,
                                                    ids: [record.id ?? 0],
                                                })
                                                    .then((response) => response.json())
                                                    .then((res: any) => {
                                                        if (res.code === 200) {
                                                            messageService.success("审批废弃");
                                                            resolve2(true);
                                                            resolve(true);
                                                        } else {
                                                            reject2(false);
                                                        }
                                                    })
                                                    .catch((err) => {
                                                        reject2(false);
                                                    });
                                            });
                                        },
                                        onCancel() {
                                            resolve(false);
                                        },
                                    });
                                });
                                if (result) {
                                    refresh();
                                }
                            }}
                        >
                            废弃
                        </Button>
                    </Flex>
                );
            },
        },
    ];

    useEffect(() => {
        getDepartmentList();
        setScrollY(getTableScroll());
    }, []);

    const refresh = () => {
        tableRef.current?.reset?.();
    };

    // 获取一层组织编码
    const getFirstDepartmentCode = (orgId: string) => {
        const currentOrgList = getNodeWithAncestors(departmentList, orgId, "dept_code");

        for (const item of currentOrgList) {
            if (item.dept_level === 10) {
                return item.dept_code;
            }
        }

        return null;
    };

    // 校验是否允许添加
    const isAllowChange = (flowSelectedList: MyFlowResp[]) => {
        const current = flowSelectedList?.[0];

        if (current) {
            const currentOrdId = Array.isArray(current.orgId) ? "-1" : current.orgId?.split?.("/")?.reverse()?.[0];
            const currentFirstDepartmentCode = getFirstDepartmentCode(currentOrdId);

            for (const item of flowSelectedList) {
                // 岗位名称是否相同
                if (current.jobNameInner !== item.jobNameInner) {
                    messageService.error("岗位名称(对内)不一致，请选择相同岗位名称");
                    return false;
                }

                // 招聘批次是否相同
                if (current.hiringBatch !== item.hiringBatch) {
                    messageService.error("招聘批次不一致，请选择相同招聘批次");
                    return false;
                }

                // 需求部门一级组织是否相同
                const itemOrgId = Array.isArray(item.orgId) ? "-1" : item.orgId?.split?.("/")?.reverse()?.[0];
                const itemFirstDepartmentCode = getFirstDepartmentCode(itemOrgId);
                if (currentFirstDepartmentCode !== itemFirstDepartmentCode) {
                    messageService.error("需求部门一级组织不一致，请选择相同需求部门一级组织");
                    return false;
                }
            }

            return true;
        }

        return false;
    };

    // 单个招聘需求直接发布
    const handleSingleFlowPublish = async (
        flowSelectedList: MyFlowResp[]
    ): Promise<{ status: boolean; msg: string }> => {
        return new Promise((resolve, reject) => {
            const params: PublishMyFlowReq = {
                ...flowSelectedList?.[0],
                allocationMethod: 1,
                specIds: [flowSelectedList?.[0]?.id ?? "-1"].join(","),
            };
            delete params.id;

            publishJobFlowApi(params)
                .then((resp) => resp.json())
                .then((resp) => {
                    // TODO 替换对应保存数据
                    const res = resp as RespParams<any>;
                    if (res.code === 200) {
                        resolve({ status: true, msg: res?.msg ?? "发布成功" });
                    } else {
                        resolve({ status: false, msg: res?.msg ?? "发布失败" });
                    }
                })
                .catch(() => {
                    reject({ status: false, msg: "发布失败" });
                });
        });
    };

    // 发布需求
    const handlePublish = async () => {
        setShowFlowTable(true);
        const timer = setTimeout(() => {
            flowTableRef.current?.tableRef?.clearSelected?.();
            flowTableRef.current?.tableRef?.reset?.();
            clearTimeout(timer);
        }, 100);
    };

    const handleExport = () => {
        const formValue = formRef.current?.getFieldsValue();
        const params = { orgId: "", position: "", status: "", outPosition: "", fileName: "招聘需求.xlsx" };
        params.orgId = formValue.orgName?.[formValue?.orgName?.length - 1] ?? "";
        if (formValue.jobNameInner) {
            params.position = currentJobList?.map((item) => item.desc)?.join("/") ?? "";
        } else {
            params.position = "";
        }
        params.status = formValue?.status ?? "";
        params.outPosition = formValue?.jobNameOuter ?? "";

        exportDataApi(params);
    };

    // 处理合并逻辑（原有逻辑）
    const handleMergeProcess = async (flowSelectedList: MyFlowResp[]) => {
        const valid = isAllowChange(flowSelectedList);
        if (!valid) return;

        const ids = flowSelectedList.map((item) => item.id ?? -1);
        const res: RespParams<JobFlowAggregationResp> = await (await getJobFlowAggregationApi(ids)).json();
        if (res.code === 200) {
            const list: FlowItemParams[] = [
                { ...defaultFlowItem, jobId: nanoid(), jobTags: [...JobTags], assignment: 1 },
            ];
            list.forEach((item) => {
                item.jobNameInnerPath = flowSelectedList[0].jobNameInner;
                if (typeof flowSelectedList[0].jobNameInnerPath === "string") {
                    setJobNameInnerPath(flowSelectedList[0].jobNameInnerPath);
                }
                item.jobNameInner = flowSelectedList[0].jobNameInner;
                item.hiringBatch = flowSelectedList[0].hiringBatch;
                item.hiringPersonNum = res.data.hiringPersonNum;
                item.offerTarget = res.data.offerTarget;
                item.orgId = res.data.orgId.split("/");
                item.orgName = res.data.orgName;
                item.educational = res.data.educational.split("/");
                item.jobLocation = res.data.jobLocation.split("/");
                item.topTalents = parseFloat((res.data.topTalents * 100).toFixed(2));
                item.excellentTalents = parseFloat((res.data.excellentTalents * 100).toFixed(2));
                item.regularTalents = parseFloat((res.data.regularTalents * 100).toFixed(2));
                item.jobJD = flowSelectedList?.[0]?.jobJD;
            });

            const newFlows = {
                ...flows,
                ...list?.[0],
            };

            setFlows(newFlows);
            setTimeout(() => {
                setShowPublic(true);
            }, 200);
        }
    };

    // 处理分批次逻辑
    const handleBatchProcess = async (flowSelectedList: MyFlowResp[]) => {
        let successCount = 0;
        let failCount = 0;

        try {
            // 串行处理每个招聘需求
            for (let i = 0; i < flowSelectedList.length; i++) {
                const flowItem = flowSelectedList[i];

                try {
                    const params: PublishMyFlowReq = {
                        ...flowItem,
                        allocationMethod: 1,
                        specIds: [flowItem?.id ?? "-1"].join(","),
                    };
                    delete params.id;
                    const resp = await publishJobFlowApi(params);
                    const res = (await resp.json()) as RespParams<any>;

                    if (res.code === 200) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (error) {
                    failCount++;
                }
            }

            // 显示处理结果
            if (successCount === flowSelectedList.length) {
                messageService.success(`所有 ${successCount} 条招聘需求发布成功`);
            } else if (successCount > 0) {
                messageService.warning(`成功发布 ${successCount} 条，失败 ${failCount} 条`);
            } else {
                messageService.error(`所有 ${flowSelectedList.length} 条招聘需求发布失败`);
            }
        } catch (error) {
            messageService.error("批量发布过程中出现异常");
        }
        setShowFlowTable(false);
    };

    // 执行选中的处理方式
    const executeSelectedProcess = async () => {
        const flowSelectedList = selectedData;
        setShowSelectTypeLoading(true);
        if (selectedProcessType === "merge") {
            await handleMergeProcess(flowSelectedList);
        } else if (selectedProcessType === "batch") {
            await handleBatchProcess(flowSelectedList);
        }

        setShowSelectType(false);
        setShowSelectTypeLoading(false);
    };

    const handleAddFlow = async () => {
        const flowSelectedList = flowTableRef.current?.selectedData ?? [];
        setSelectedData(flowSelectedList);

        if (flowSelectedList.length === 0) {
            messageService.warning("请选择招聘需求");
            return;
        }

        if (flowSelectedList.length === 1) {
            const confirmRes: { status: boolean; msg: string } = await new Promise((resolve) => {
                Modal.confirm({
                    title: "提示",
                    icon: <ExclamationCircleFilled />,
                    content: "当前只选中了1条招聘需求，点击确定后将直接发布，请确定是否继续？",
                    onOk: async () => {
                        try {
                            const result = await handleSingleFlowPublish(flowSelectedList);
                            resolve(result);
                        } catch (error) {
                            resolve({ status: false, msg: "发布失败" });
                        }
                    },
                });
            });

            if (confirmRes?.status) {
                setShowFlowTable(false);
                messageService.success(confirmRes.msg);
                refresh();
            }

            return;
        }

        // 多个选择时显示处理方式选择对话框
        setShowSelectType(true);
    };

    const handleSubmit = async () => {
        try {
            const validateRes = await flowItemRef.current?.getForm().validateFields();
            if (validateRes) {
                const params: any = flowItemRef.current?.getForm().getFieldsValue();
                params.jobJD = flowItemRef.current?.richTextRef?.current?.getHTML() ?? "";
                params.jobJD = formatHtmlWithLists(params.jobJD);
                if (Array.isArray(params.orgId)) {
                    params.orgId = params?.orgId?.join("/");
                }
                params.jobNameInnerPath = jobNameInnerPath;
                if (Array.isArray(params.educational)) {
                    params.educational = params?.educational?.join("/");
                }
                if (Array.isArray(params.jobLocation)) {
                    params.jobLocation = params?.jobLocation?.join("/");
                }

                // 校验人才比例之后是否为100%
                if (Math.abs(params.topTalents + params.excellentTalents + params.regularTalents - 100) > 0.01) {
                    messageService.error(
                        `${params?.orgName ?? "默认组织"}-${params?.jobNameInner ?? "默认岗位"} 人才比例之和必须为100%`
                    );
                    return false;
                }

                // 检验职位JD是否符合正则校验
                const regex1 = /(工作职责|岗位职责|主要职责)[:：]\s*(?=\S)/g;
                const regex2 = /(职位要求|岗位要求|任职要求)[:：]\s*(?=\S)/g;
                if (!regex1.test(params.jobJD) || !regex2.test(params.jobJD)) {
                    messageService.error(
                        <Flex vertical align="start">
                            <div>岗位JD格式错误，格式必须为: </div>
                            <div>工作职责/岗位职责/主要职责: </div>
                            <div>xxxxxxx</div>
                            <div>职位要求/岗位要求/任职要求:</div>
                            <div>xxxxxxx</div>
                        </Flex>
                    );
                    return false;
                }

                params.tags = [];
                params.jobTags = flows?.jobTags;
                params.jobTags?.forEach((item: TagsItem) => {
                    item?.children?.forEach((item: TagsItem) => {
                        params.tags.push(...(item.value?.map((item2: TagsResp) => item2.id) ?? []));
                    });
                });
                params.language = params?.language ?? "";
                params.reason = params?.reason ?? "";
                delete params.jobTags;

                params.allocationMethod = assignment;
                const selectedData = flowTableRef.current?.selectedData ?? [];
                params.specIds = selectedData.map((item) => item.id).join(",");
                params.proportionValues = {};

                if (params.allocationMethod === 3) {
                    selectedData.forEach((item) => {
                        if (item.id) {
                            params.proportionValues[item.id] = item.assignment;
                        }
                    });
                }

                setConfirmLoading(true);

                const res: RespParams<any> = await (await publishJobFlowApi(params)).json();
                if (res.code === 200) {
                    messageService.success(res.msg ?? "发布成功");
                    setConfirmLoading(false);
                    setShowPublic(false);
                    setShowFlowTable(false);
                    refresh();
                }
            }
        } catch (error) {
            setConfirmLoading(false);
        }
    };

    const expandedRender = (record: any) => {
        return expandedLoading ? (
            <Spin />
        ) : (
            <Table rowKey="id" pagination={false} dataSource={expandedTableData[record.id]}>
                <Table.Column title="需求部门" dataIndex="orgName" key="orgName" />
                <Table.Column title="需求人数" dataIndex="hiringPersonNum" key="hiringPersonNum" />
                <Table.Column title="完成率" dataIndex="completionRate" key="completionRate" />
                <Table.Column title="体系需求负责人" dataIndex="createUserId" key="createUserId" />
                <Table.Column title="创建时间" dataIndex="createTime" key="createTime" width="200" ellipsis />
                <Table.Column title="修改时间" dataIndex="updateTime" key="updateTime" width="200" ellipsis />
                <Table.Column
                    title="操作"
                    dataIndex="operation"
                    key="operation"
                    fixed="right"
                    width="100"
                    render={(_, record) => {
                        return (
                            <Button
                                type="link"
                                onClick={() => {
                                    setCurrentJobType("editChildrenJob");
                                    setCurrentJobId(record.id);
                                    handleDetail(record.flowInstanceId);
                                }}
                            >
                                详情
                            </Button>
                        );
                    }}
                />
            </Table>
        );
    };

    // 获取详情数据
    const handleDetail = async (flowInstanceId: string) => {
        setCurrentFlowId(flowInstanceId);
        setShowJobDetail(true);
        const timer = setTimeout(() => {
            flowDetailRef.current?.refresh();
            clearInterval(timer);
        }, 100);
    };

    return (
        <Layout>
            <Header title="招聘需求" />
            <Layout.Content>
                <ProTable
                    rowKey="id"
                    actionRef={tableRef}
                    formRef={formRef}
                    columns={columns}
                    scroll={{ x: "max-content", y: scrollY }}
                    options={{ reload: false }}
                    pagination={{
                        defaultCurrent: 1,
                        defaultPageSize: 10,
                        hideOnSinglePage: false,
                        showSizeChanger: true,
                        pageSizeOptions: [10, 20, 50, 100],
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    toolBarRender={() => [
                        <Button key="publish" type="primary" onClick={handlePublish}>
                            发布需求
                        </Button>,
                        <Button key="export" type="primary" onClick={handleExport}>
                            导出
                        </Button>,
                    ]}
                    expandable={{
                        expandedRowRender: (record) => expandedRender(record),
                        expandedRowKeys: expandedRowKeys,
                        onExpand: async (expanded, record) => {
                            if (expanded) {
                                setExpandedKeys((prev) => [...prev, record?.id ?? -1]);
                                setExpandedLoading(true);
                                try {
                                    const res: RespParams<MyFlowResp> = await (
                                        await getJobFlowChildrenByIdApi(record.id ?? -1)
                                    ).json();

                                    if (res.code === 200) {
                                        setExpandedTableData((prev) => ({
                                            ...prev,
                                            [record.id as number]: res?.data?.jobSpecs ?? [],
                                        }));

                                        setExpandedLoading(false);
                                    }
                                } catch (err) {
                                    setExpandedLoading(false);
                                }
                            } else {
                                setExpandedKeys((prev) => prev.filter((item) => item !== record.id));
                            }
                        },
                    }}
                    request={async (params) => {
                        const form: MyFlowReq = {
                            page: (params.current ?? 1) - 1,
                            size: params.pageSize ?? 10,
                        };
                        form.orgId = params.orgName?.[params?.orgName?.length - 1] ?? "";

                        if (params.jobNameInner) {
                            form.position = currentJobList?.map((item) => item.desc)?.join("/") ?? "";
                        } else {
                            form.position = "";
                        }

                        form.status = params?.status ?? "";
                        form.outPosition = params?.jobNameOuter ?? "";

                        const res: RespPaginationParams<MyFlowResp> = await (await getMyFlowListApi(form)).json();
                        if (res.code === 200) {
                            return {
                                data: res.data?.records.map((item) => {
                                    item.assignment = 1;
                                    return item;
                                }),
                                success: true,
                                total: res.data?.total,
                            };
                        }
                        return {
                            data: [],
                            success: false,
                            total: 0,
                        };
                    }}
                    onReset={() => setExpandedKeys([])}
                />
            </Layout.Content>
            <ChatDialog
                width="75%"
                title="待发布需求"
                open={showFlowTable}
                onCancel={() => setShowFlowTable(false)}
                confirmLoading={false}
                onOk={() => handleAddFlow()}
            >
                <FlowTable ref={flowTableRef} departmentList={departmentList} />
            </ChatDialog>
            <ChatDialog
                width="75%"
                title="发布"
                open={showPublic}
                confirmLoading={confirmLoading}
                onCancel={() => setShowPublic(false)}
                onOk={() => handleSubmit()}
            >
                <div style={{ padding: "16px" }}>
                    <FlowItem
                        ref={flowItemRef}
                        mode="publish"
                        item={flows!}
                        handleJobNameChange={() => {}}
                        handleOrgChange={() => {}}
                        handleChangeLoading={(val) => {
                            setConfirmLoading(val);
                        }}
                        handleAssignmentChange={(val) => {
                            setAssignment(val);
                        }}
                        customChildren={
                            <AddFlowDetailChildren
                                tableData={selectedData}
                                isEdit={true}
                                allocationMethod={assignment}
                            />
                        }
                    />
                </div>
            </ChatDialog>
            <ChatDialog
                width="75%"
                title="需求详情"
                open={showJobDetail}
                confirmLoading={detailLoading}
                footer={null}
                onCancel={() => {
                    tableRef.current?.reload?.();
                    setShowJobDetail(false);
                }}
            >
                <FlowDetail
                    ref={flowDetailRef}
                    flowId={currentFlowId}
                    jobId={currentJobId}
                    handleChangeLoading={(val) => setDetailLoading(val)}
                    type="JobFlowTodo"
                    mode={currentJobType}
                />
            </ChatDialog>
            <ChatDialog
                width="30%"
                title="选择处理方式"
                open={showSelectType}
                confirmLoading={showSelectTypeLoading}
                onCancel={() => setShowSelectType(false)}
                onOk={() => {
                    executeSelectedProcess();
                }}
            >
                <div style={{ padding: "16px" }}>
                    <Radio.Group value={selectedProcessType} onChange={(e) => setSelectedProcessType(e.target.value)}>
                        <Radio value="merge">选择合并处理</Radio>
                        <Radio value="batch">选择分批次处理</Radio>
                    </Radio.Group>
                </div>
            </ChatDialog>
        </Layout>
    );
};

export default FlowPublish;
