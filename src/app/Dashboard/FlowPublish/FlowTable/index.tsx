import ProTable, { ActionType, ProColumnType } from "@ant-design/pro-table";
import { Department, FlowTableExposeHandle, Job, MyFlowReq, MyFlowResp } from "@/app/store/modules/dashboard/jobFlow";
import { RespPaginationParams, RespParams } from "@/app/typing";
import { getMyJobListApi } from "@/app/request/modules/dashboard";
import { forwardRef, useEffect, useImperativeHandle, useLayoutEffect, useRef, useState } from "react";
import { getTableScroll } from "@/app/utils";
import { FormInstance } from "antd";
import { getJobListApi } from "@/app/request/modules/common";
import { nanoid } from "nanoid";

const FlowTable = forwardRef<FlowTableExposeHandle, { departmentList: Department[] }>((props, ref) => {
    useImperativeHandle(ref, () => ({
        tableRef: flowTableRef.current,
        selectedData: selectedData,
    }));

    const { departmentList } = props;

    const flowTableRef = useRef<ActionType>(null);
    const formRef = useRef<FormInstance>();
    const [selectedData, setSelectedData] = useState<MyFlowResp[]>([]);
    const [allJobList, setAllJobList] = useState<Job[]>([]);
    const [scrollY, setScrollY] = useState<number>();
    const [currentJobList, setCurrentJobList] = useState<Job[]>([]);
    const [id, setId] = useState<string>("");

    useEffect(() => {
        setId(nanoid());
    }, []);

    useLayoutEffect(() => {
        setScrollY(getTableScroll({ id: id }));
    }, [id, selectedData]);

    const handleChangeSelectedData = (row: any) => {
        if (Array.isArray(row)) {
            setSelectedData(row);
        } else {
            const current = selectedData.find((item) => item.id === row.id);
            if (current) {
                setSelectedData((prev) => prev.filter((item) => item.id !== row.id));
            } else {
                setSelectedData([...selectedData, row]);
            }
        }
    };

    const columns: ProColumnType<MyFlowResp>[] = [
        {
            title: "需求部门",
            dataIndex: "orgName",
            width: 350,
            ellipsis: true,
            valueType: "cascader",
            fieldProps: {
                expandTrigger: "hover",
                placeholder: "请选择需求部门",
                showSearch: {
                    limit: Infinity,
                },
                options: departmentList,
                changeOnSelect: true,
                fieldNames: {
                    label: "dept_name",
                    value: "dept_code",
                },
                optionRender: (option: Department) => {
                    return (
                        <div
                            onClick={(e) => {
                                // 禁用节点，禁止点击后选中
                                if (option.isDisabled) {
                                    e.stopPropagation();
                                }
                            }}
                            style={{
                                color: option.isDisabled ? "var(--sub-text-color-2)" : "inherit",
                                cursor: option.isDisabled ? "not-allowed" : "pointer",
                            }}
                        >
                            {option.dept_name}
                        </div>
                    );
                },
                onBlur: async () => {
                    const value = formRef.current?.getFieldValue("orgName");
                    if (value && value.length > 0) {
                        const code = value?.[value?.length - 1];
                        const res: RespParams<Job[]> = await (await getJobListApi({ orgNo: code })).json();
                        if (res.code === 200) {
                            setAllJobList(res.data);
                        }
                    }
                },
                onChange: (value) => {
                    if (!value) {
                        setAllJobList([]);
                        formRef.current?.setFieldsValue({ jobNameInner: undefined });
                    }
                },
            },
        },
        {
            title: "职位名称",
            dataIndex: "jobNameInner",
            width: 350,
            ellipsis: true,
            valueType: "cascader",
            fieldProps: {
                expandTrigger: "hover",
                placeholder: "请选择职位名称",
                showSearch: {
                    limit: Infinity,
                },
                options: allJobList,
                changeOnSelect: true,
                fieldNames: {
                    label: "desc",
                    value: "code",
                },
                onChange: (val: string[], options: Job[]) => {
                    setCurrentJobList(options);
                },
            },
        },
        {
            title: "招聘批次",
            dataIndex: "hiringBatch",
            search: false,
            width: 150,
        },
        {
            title: "学历要求",
            dataIndex: "educational",
            search: false,
            width: 150,
        },
        {
            title: "工作地点",
            dataIndex: "jobLocation",
            search: false,
            width: 150,
        },
        {
            title: "需求人数",
            dataIndex: "hiringPersonNum",
            search: false,
            width: 150,
        },
        {
            title: "需求负责人",
            dataIndex: "createUser",
            search: false,
            width: 150,
            render: (_, record) => {
                return record.createUser?.empId + "-" + record.createUser?.empName;
            },
        },
        {
            title: "创建时间",
            dataIndex: "createTime",
            search: false,
            ellipsis: true,
            width: 200,
        },
        {
            title: "修改时间",
            dataIndex: "updateTime",
            search: false,
            ellipsis: true,
            width: 200,
        },
    ];

    return (
        <ProTable
            id={id}
            rowKey="id"
            formRef={formRef}
            actionRef={flowTableRef}
            columns={columns}
            options={{ reload: false }}
            scroll={{ x: "max-content", y: scrollY }}
            manualRequest
            onLoad={() => setScrollY(getTableScroll({ id: id }))}
            pagination={{
                defaultCurrent: 1,
                defaultPageSize: 10,
                hideOnSinglePage: false,
                showSizeChanger: true,
                pageSizeOptions: [10, 20, 50, 100],
                showTotal: (total) => `共 ${total} 条`,
            }}
            rowSelection={{
                type: "checkbox",
                preserveSelectedRowKeys: true,
                selectedRowKeys: selectedData.map((item) => item?.id ?? 0),
                onChange: (_, selectedRows) => {
                    handleChangeSelectedData(selectedRows);
                },
            }}
            onRow={(record) => {
                return {
                    onClick: () => {
                        handleChangeSelectedData(record);
                    },
                };
            }}
            request={async (params) => {
                const form: MyFlowReq = {
                    page: (params.current ?? 1) - 1,
                    size: params.pageSize ?? 10,
                };
                form.orgId = params.orgName?.[params?.orgName?.length - 1] ?? "";
                if (params.jobNameInner) {
                    form.position = currentJobList?.map((item) => item.desc)?.join("/") ?? "";
                } else {
                    form.position = "";
                }
                form.status = 1;

                const res: RespPaginationParams<MyFlowResp> = await (await getMyJobListApi(form)).json();

                if (res.code === 200) {
                    return {
                        data: res.data?.records.map((item) => {
                            item.assignment = 1;
                            return item;
                        }),
                        success: true,
                        total: res.data?.total,
                    };
                }

                return {
                    data: [],
                    success: false,
                    total: 0,
                };
            }}
        />
    );
});
FlowTable.displayName = "FlowTable";

export default FlowTable;
