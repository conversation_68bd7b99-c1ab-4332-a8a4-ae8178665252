import React from "react";
import Icon from "@ant-design/icons";
import { Input, Divider, Badge } from "antd";
import MessageIcon from "../../icons/dashboard/message.svg";
import styles from "./index.module.scss";
import { useAppConfig } from "@/app/store";

const { Search } = Input;

export function TitleContent({ handleSearch }: { handleSearch: (val: string) => void }) {
    const { sideBarisCollapsed } = useAppConfig();
    return (
        <div className={styles["title-content"]}>
            <div className={styles["title-content-title"]} style={{ marginLeft: sideBarisCollapsed ? "40px" : "0" }}>
                工作台
            </div>
            <div className={styles["title-content-search"]}>
                <div>
                    <Search
                        placeholder="请输入招聘需求岗位名称/发起人搜索"
                        style={{ width: 371 }}
                        allowClear
                        onSearch={(val) => handleSearch(val)}
                    />
                </div>

                <Divider type="vertical"></Divider>
                <div className={styles["title-content-search-message"]}>
                    <Badge count={"5"}>
                        <Icon component={() => <MessageIcon />} size={24}></Icon>
                    </Badge>
                </div>
            </div>
        </div>
    );
}
