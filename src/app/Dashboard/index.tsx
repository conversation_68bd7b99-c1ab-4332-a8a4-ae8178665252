import { Tabs } from "antd";
import Board from "./board";
import Flow from "./Flow";
import styles from "./index.module.scss";
import { TitleContent } from "./title-content";
import { useLocation } from "react-router-dom";
import { useState } from "react";
// 工作台看板tab key
const TAB_DASHBOARD = "2";

export default function Dashboard() {
    const location = useLocation();
    const searchParams = new URLSearchParams(location.search);
    let defaultActiveKey = location.state?.tab || TAB_DASHBOARD;
    let type: string | null = null;
    let flowId: string | null = null;

    const [keyword, setKeyword] = useState<string>("");

    if (searchParams.size > 0) {
        type = searchParams.get("type");
        flowId = searchParams.get("flowId");

        if (type === "1") {
            defaultActiveKey = "2";
        }
    }

    const items = [
        // {
        //     label: "看板",
        //     key: "1",
        //     children: <Board />,
        // },
        {
            label: "流程审批",
            key: "2",
            children: <Flow type={type} flowId={flowId} keyword={keyword} />,
        },
    ];

    return (
        <div className={styles["tabs-wrapper"]}>
            <TitleContent handleSearch={(val) => setKeyword(val)} />
            <Tabs defaultActiveKey={defaultActiveKey} items={items} />
        </div>
    );
}
