export const defaultFlowItem = {
    jobId: "",
    orgName: "",
    jobNameInnerPath: [],
    jobNameInner: "",
    jobNameOuter: "",
    desc: "",
    orgId: [],
    hiringBatch: null,
    hiringPersonNum: null,
    offerTarget: null,
    jobType: null,
    topTalents: null,
    excellentTalents: null,
    regularTalents: null,
    jobLocation: [],
    educational: [],
    jobJD: "",
    jobTags: [],
};

export const hiringBatchData = [
    {
        value: "26届提前批",
        label: "26届提前批",
    },
    {
        value: "26届正式批",
        label: "26届正式批",
    },
    {
        value: "26届春招补录",
        label: "26届春招补录",
    },
];

export const jobTypeData = [
    {
        value: "核心岗位",
        label: "核心岗位",
    },
    {
        value: "普通岗位",
        label: "普通岗位",
    },
    {
        value: "基础岗位",
        label: "基础岗位",
    },
];

export const jobLocationData = [
    {
        value: "全国",
        label: "全国",
    },
    {
        value: "深圳",
        label: "深圳",
    },
    {
        value: "东莞",
        label: "东莞",
    },
    {
        value: "长春",
        label: "长春",
    },
    {
        value: "宁波",
        label: "宁波",
    },
    {
        value: "杭州",
        label: "杭州",
    },
    {
        value: "岳阳",
        label: "岳阳",
    },
    {
        value: "太原",
        label: "太原",
    },
    {
        value: "北京",
        label: "北京",
    },
    {
        value: "常州",
        label: "常州",
    },
    {
        value: "苏州",
        label: "苏州",
    },
    {
        value: "西安",
        label: "西安",
    },
    {
        value: "南京",
        label: "南京",
    },
    {
        value: "天津",
        label: "天津",
    },
    {
        value: "佛山",
        label: "佛山",
    },
    {
        value: "顺德",
        label: "顺德",
    },
    {
        value: "嘉兴",
        label: "嘉兴",
    },
    {
        value: "嘉善",
        label: "嘉善",
    },
    {
        value: "上海",
        label: "上海",
    },
    {
        value: "南昌",
        label: "南昌",
    },
    {
        value: "大连",
        label: "大连",
    },
    {
        value: "苏州吴淞江",
        label: "苏州吴淞江",
    },
    {
        value: "海外",
        label: "海外",
    },
    {
        value: "济南",
        label: "济南",
    },
    {
        value: "武汉",
        label: "武汉",
    },
    {
        value: "芜湖",
        label: "芜湖",
    },
];

export const educationalData = [
    {
        value: "大专",
        label: "大专",
    },
    {
        value: "本科",
        label: "本科",
    },
    {
        value: "研究生",
        label: "研究生",
    },
    {
        value: "博士",
        label: "博士",
    },
    {
        value: "本科/研究生",
        label: "本科/研究生",
    },
];

export const languageList = [
    { label: "一般", value: "一般" },
    { label: "良好", value: "良好" },
    { label: "精通", value: "精通" },
];

export const JobTags = [
    {
        label: "硬性门槛层",
        children: [
            {
                label: "学历",
                value: [],
            },
            {
                label: "院校背景",
                value: [],
            },
            {
                label: "专业要求",
                value: [],
            },
            {
                label: "语言能力",
                value: [],
            },
        ],
    },
    {
        label: "能力适配层",
        children: [
            {
                label: "实习经历",
                value: [],
            },
            {
                label: "科研经历",
                value: [],
            },
            {
                label: "竞赛经历",
                value: [],
            },
            {
                label: "技能证书",
                value: [],
            },
            {
                label: "专业能力",
                value: [],
            },
            {
                label: "工具掌握",
                value: [],
            },
        ],
    },
    {
        label: "潜力预判层",
        children: [
            {
                label: "抗压韧性",
                value: [],
            },
            {
                label: "学习能力",
                value: [],
            },
            {
                label: "学生干部经历",
                value: [],
            },
            {
                label: "表彰经历",
                value: [],
            },
        ],
    },
];

export const assignmentList = [
    {
        value: 1,
        label: "平均分配",
    },
    {
        value: 2,
        label: "手动分配",
    },
    {
        value: 3,
        label: "比例分配",
    },
];

/**
 * 手动分配
 */
export const ASSIGNMENT_TYPE_MANUAL = 2;

export const statusList = [
    { label: "已废弃", value: -1, type: "error" },
    { label: "需求审批中", value: 0, type: "processing" },
    { label: "未发布", value: 1, type: "processing" },
    { label: "发布审批中", value: 2, type: "processing" },
    { label: "进行中", value: 3, type: "processing" },
    { label: "已完成", value: 4, type: "success" },
    { label: "已超额", value: 5, type: "error" },
    { label: "已关闭", value: 6, type: "default" },
];

// 待办类型
export const TaskTypeList = [
    {
        key: "JobFlowTodo",
        label: "待办",
        status: "W",
    },
    {
        key: "JobFlowDone",
        label: "已办",
        status: "D",
    },
    {
        key: "JobFlowDeprecated",
        label: "已废弃",
        status: "C",
    },
    {
        key: "JobFlowHistory",
        label: "审批历史",
        isHistory: true,
    },
];
