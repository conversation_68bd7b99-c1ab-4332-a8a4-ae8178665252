export type Edge = "top" | "right" | "bottom" | "left";

/** 拖拽状态类型 */
export type DraggableState = { type: "idle" } | { type: "preview"; container: HTMLElement } | { type: "dragging" };

export const draggingState: DraggableState = { type: "dragging" };
export const idleState: DraggableState = { type: "idle" };

/** 阶段拖拽数据 */
export interface StageItemData {
    type: "stage";
    item: any;
    index: number;
    [key: string]: unknown;
    [key: symbol]: unknown;
}

/** 状态拖拽数据 */
export interface StatusItemData {
    type: "status";
    item: any;
    stageId: string;
    index: number;
    [key: string]: unknown;
    [key: symbol]: unknown;
}

/** 拖拽数据联合类型 */
export type DragData = StageItemData | StatusItemData;

/** 重新排序阶段参数类型 */
export interface ReorderStageParams {
    startIndex: number;
    indexOfTarget: number;
    closestEdgeOfTarget: Edge | null;
}

/** 重新排序状态参数类型 */
export interface ReorderStatusParams {
    stageId: string;
    startIndex: number;
    indexOfTarget: number;
    closestEdgeOfTarget: Edge | null;
}

/** 重新排序数组 */
export function reorder<T>({
    list,
    startIndex,
    finishIndex,
}: {
    list: T[];
    startIndex: number;
    finishIndex: number;
}): T[] {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(finishIndex, 0, removed);
    return result;
}

/** 获取重新排序的目标索引 */
export function getReorderDestinationIndex({
    closestEdgeOfTarget,
    indexOfTarget,
}: {
    startIndex: number;
    closestEdgeOfTarget: Edge | null;
    indexOfTarget: number;
    axis?: "vertical" | "horizontal";
}): number {
    const isGoingBelow = closestEdgeOfTarget === "bottom" || closestEdgeOfTarget === "right";
    const isGoingAbove = closestEdgeOfTarget === "top" || closestEdgeOfTarget === "left";

    if (isGoingBelow) {
        return indexOfTarget + 1;
    }

    if (isGoingAbove) {
        return indexOfTarget;
    }

    return indexOfTarget;
}

/** 提取最近边缘信息 */
export function extractClosestEdge(data: Record<string | symbol, unknown>): Edge | null {
    const edge = data["closest-edge"];
    if (edge === "top" || edge === "bottom" || edge === "left" || edge === "right") {
        return edge;
    }
    return null;
}

/** 附加最近边缘信息 */
export function attachClosestEdge(
    data: Record<string | symbol, unknown>,
    options: {
        element: Element;
        input: {
            clientX: number;
            clientY: number;
        };
        allowedEdges: Edge[];
    }
): Record<string | symbol, unknown> {
    const { element, input, allowedEdges } = options;
    const rect = element.getBoundingClientRect();

    const relativeX = input.clientX - rect.left;
    const relativeY = input.clientY - rect.top;

    const distanceToTop = relativeY;
    const distanceToBottom = rect.height - relativeY;
    const distanceToLeft = relativeX;
    const distanceToRight = rect.width - relativeX;

    let closestEdge: Edge | null = null;
    let minDistance = Infinity;

    if (allowedEdges.includes("top") && distanceToTop < minDistance) {
        minDistance = distanceToTop;
        closestEdge = "top";
    }

    if (allowedEdges.includes("bottom") && distanceToBottom < minDistance) {
        minDistance = distanceToBottom;
        closestEdge = "bottom";
    }

    if (allowedEdges.includes("left") && distanceToLeft < minDistance) {
        minDistance = distanceToLeft;
        closestEdge = "left";
    }

    if (allowedEdges.includes("right") && distanceToRight < minDistance) {
        minDistance = distanceToRight;
        closestEdge = "right";
    }

    return {
        ...data,
        "closest-edge": closestEdge,
    };
}

/** 获取阶段拖拽数据 */
export function getStageItemData({ item, index }: { item: any; index: number }): StageItemData {
    return {
        type: "stage",
        item,
        index,
    };
}

/** 获取状态拖拽数据 */
export function getStatusItemData({
    item,
    stageId,
    index,
}: {
    item: any;
    stageId: string;
    index: number;
}): StatusItemData {
    return {
        type: "status",
        item,
        stageId,
        index,
    };
}

/** 验证是否为拖拽数据 */
export function isDragData(data: Record<string | symbol, unknown>): data is DragData {
    return (
        typeof data === "object" && data !== null && "type" in data && (data.type === "stage" || data.type === "status")
    );
}
