import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import debounce from "lodash/debounce";
import { getInterviewTemplateListApi } from "@/app/request/modules/dashboard/jobManage";

/** 分页状态接口 */
interface PaginationState {
    page: number;
    size: number;
    hasMore: boolean;
}

/** 模板项接口 */
interface TemplateItem {
    id: string;
    title: string;
}

/** 选项接口 */
interface OptionItem {
    label: string;
    value: string;
}

/** Hook返回值接口 */
interface UseInterviewTemplateSelectReturn {
    options: OptionItem[];
    fetching: boolean;
    handleSearch: (value: string) => void;
    handleClear: () => void;
    handlePopupScroll: (e: React.UIEvent<HTMLDivElement>) => void;
    handleDropdownVisibleChange: (open: boolean) => void;
}

/**
 * 面试模板选择器的自定义Hook
 * @param orgId 组织ID
 * @returns 模板选择器所需的状态和方法
 */
export const useInterviewTemplateSelect = (orgId: string): UseInterviewTemplateSelectReturn => {
    const [fetching, setFetching] = useState(false);
    const [options, setOptions] = useState<OptionItem[]>([]);
    const [currentSearch, setCurrentSearch] = useState<string>("");
    const [shouldLoadData, setShouldLoadData] = useState(false); // 控制是否需要加载数据
    const [isLoadMore, setIsLoadMore] = useState(false); // 控制是否为加载更多操作
    const fetchRef = useRef(0);

    const [pagination, setPagination] = useState<PaginationState>({
        page: 0,
        size: 50,
        hasMore: true,
    });

    /**
     * 获取模板列表数据
     * @param search 搜索关键词
     * @param page 页码，从0开始
     * @param size 每页条数
     * @param isLoadMore 是否为加载更多操作
     * @returns 格式化后的选项数据
     */
    const fetchTemplateList = useCallback(async (search: string, page = 0, size = 10, isLoadMore = false) => {
        try {
            const response: any = await getInterviewTemplateListApi({
                page,
                size,
                title: search,
                orgId,
            });
            const res = await response.json();

            if (res.code === 200) {
                let newOptions: OptionItem[] = [];
                let hasMore = false;

                // 处理返回的数据
                if (res.data && "content" in res.data) {
                    newOptions = res.data.content.map((item: TemplateItem) => ({
                        label: item.title,
                        value: item.id,
                    }));

                    // 判断是否还有更多数据
                    if (res.data.content.length === size) {
                        hasMore = true;
                    }
                }

                // 更新分页状态
                setPagination((prev) => ({
                    ...prev,
                    page: isLoadMore ? prev.page + 1 : 0,
                    hasMore,
                }));

                return { options: newOptions, isLoadMore };
            }
            return { options: [], isLoadMore };
        } catch (error) {
            console.error("获取面试模板列表失败", error);
            return { options: [], isLoadMore };
        }
    }, []);

    /**
     * 统一的数据加载方法
     */
    const loadTemplateData = useCallback(async () => {
        setFetching(true);

        const targetPage = isLoadMore ? pagination.page + 1 : 0;

        const { options: newOptions } = await fetchTemplateList(currentSearch, targetPage, pagination.size, isLoadMore);

        if (isLoadMore) {
            // 追加数据
            setOptions((prev) => [...prev, ...newOptions]);
            setPagination((prev) => ({ ...prev, page: targetPage, hasMore: newOptions.length === pagination.size }));
        } else {
            // 替换数据
            setOptions(newOptions);
            setPagination((prev) => ({ ...prev, page: 0, hasMore: newOptions.length === pagination.size }));
        }

        setFetching(false);
        // 重置加载更多标志
        setIsLoadMore(false);
    }, [fetchTemplateList, pagination.size, pagination.page, currentSearch, isLoadMore]);

    /**
     * useEffect: 当依赖项变更时自动加载数据
     */
    useEffect(() => {
        if (shouldLoadData && orgId) {
            // 当搜索条件变化时，重置分页状态
            if (!isLoadMore) {
                setPagination((prev) => ({ ...prev, page: 0, hasMore: true }));
            }
            loadTemplateData();
        }
    }, [currentSearch, orgId, shouldLoadData, isLoadMore, loadTemplateData]);

    /**
     * 处理搜索输入
     */
    const debounceFetcher = useMemo(() => {
        const loadOptions = (value: string) => {
            fetchRef.current += 1;
            setOptions([]);
            if (shouldLoadData) {
                setFetching(true);
            }

            // 设置搜索关键词，useEffect 会自动触发数据加载
            setCurrentSearch(value);
        };

        return debounce(loadOptions, 300);
    }, [shouldLoadData]); // 移除 loadTemplateData 依赖

    /**
     * 处理清空操作
     */
    const handleClear = useCallback(() => {
        setCurrentSearch("");
    }, []);

    /**
     * 处理下拉框滚动事件，实现滚动加载更多数据
     * @param e 滚动事件对象
     */
    const handlePopupScroll = useCallback(
        debounce((e: React.UIEvent<HTMLDivElement>) => {
            const { target } = e;
            const div = target as HTMLDivElement;

            // 判断是否滚动到底部
            if (div.scrollTop + div.clientHeight >= div.scrollHeight - 5) {
                // 如果还有更多数据且当前不在加载中，则触发加载更多
                if (pagination.hasMore && !fetching && !isLoadMore) {
                    setIsLoadMore(true);
                }
            }
        }, 200),
        [pagination.hasMore, fetching, isLoadMore]
    );

    /**
     * 处理下拉框展开/收起事件
     * @param open 下拉框是否展开
     */
    const handleDropdownVisibleChange = useCallback((open: boolean) => {
        console.log("🚀 ~ file: useInterviewTemplateSelect.ts:194 ~ handleDropdownVisibleChange:", open);
        if (open) {
            // 当下拉框展开时，如果没有数据则触发加载
            // if (options.length === 0 && !fetching) {
            setShouldLoadData(true);
            // }
        } else {
            console.log("关闭popover");
            setFetching(false);
            // 下拉框关闭时，暂停自动加载
            setShouldLoadData(false);
        }
    }, []);

    return {
        options,
        fetching,
        handleSearch: debounceFetcher,
        handleClear,
        handlePopupScroll,
        handleDropdownVisibleChange,
    };
};
