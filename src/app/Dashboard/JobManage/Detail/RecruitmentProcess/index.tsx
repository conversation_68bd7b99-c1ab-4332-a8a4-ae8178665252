"use client";

import React, { useCallback, useMemo, useEffect } from "react";
import { RecruitmentProcessProps } from "./typing";
import { ProcessStageItem } from "./components";
import { useFlowActions } from "../../../hooks/useFlowActions";
import { monitorForElements } from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import {
    isDragData,
    getReorderDestinationIndex,
    extractClosestEdge,
    type ReorderStageParams,
    type ReorderStatusParams,
} from "./dragUtils";

/**
 * 流程阶段项组件属性接口
 */
interface ProcessStageItemComponentProps {
    stage: {
        type: string;
        draggable: boolean;
        deletable: boolean;
        data?: any;
        label?: string;
        index?: number;
    };
    index?: number;
    readonly?: boolean;
}

/**
 * 招聘流程管理主组件
 * @param props 组件属性
 * @returns 招聘流程组件
 */
const RecruitmentProcess: React.FC<RecruitmentProcessProps> = ({ flowList = [], readonly = false }) => {
    const { reorderStages, reorderStatuses } = useFlowActions();

    /** 重新排序阶段 */
    const getStagesIndex = useCallback(({ startIndex, indexOfTarget, closestEdgeOfTarget }: ReorderStageParams) => {
        const toIndex = getReorderDestinationIndex({
            startIndex,
            closestEdgeOfTarget,
            indexOfTarget,
            axis: "vertical",
        });

        if (toIndex === startIndex) return;
        // 更新阶段顺序
        reorderStages(startIndex, toIndex);
    }, []);

    /** 重新排序状态 */
    const getStatusIndex = useCallback(
        ({ stageId, startIndex, indexOfTarget, closestEdgeOfTarget }: ReorderStatusParams) => {
            const toIndex = getReorderDestinationIndex({
                startIndex,
                closestEdgeOfTarget,
                indexOfTarget,
                axis: "vertical",
            });

            if (toIndex === startIndex) return;

            // 找到对应的阶段并重新排序其状态
            const stage = flowList.find((s: any) => s?.dictId === stageId);
            if (!stage || !stage?.states) return;

            reorderStatuses(stageId, startIndex, toIndex);
        },
        [flowList, reorderStatuses]
    );

    useEffect(() => {
        return monitorForElements({
            canMonitor({ source }) {
                return isDragData(source.data);
            },
            onDrop({ location, source }) {
                const target = location.current.dropTargets[0];
                if (!target) return;

                const sourceData = source.data;
                const targetData = target.data;
                if (!isDragData(sourceData) || !isDragData(targetData)) return;

                // 阶段拖拽
                if (sourceData.type === "stage" && targetData.type === "stage") {
                    const indexOfTarget = flowList.findIndex((stage: any) => stage?.dictId === targetData.item.dictId);
                    if (indexOfTarget < 0) return;

                    const closestEdgeOfTarget = extractClosestEdge(targetData);

                    getStagesIndex({
                        startIndex: sourceData.index,
                        indexOfTarget,
                        closestEdgeOfTarget,
                    });
                }
                // 状态拖拽
                else if (sourceData.type === "status" && targetData.type === "status") {
                    // 只允许同一阶段下的状态拖拽
                    if (sourceData.stageId !== targetData.stageId) return;

                    const stage = flowList.find((s: any) => s?.dictId === sourceData.stageId);
                    if (!stage || !stage?.states) return;

                    const indexOfTarget = stage.states.findIndex(
                        (status: any) => status.dictId === targetData.item.dictId
                    );
                    if (indexOfTarget < 0) return;
                    const closestEdgeOfTarget = extractClosestEdge(targetData);
                    getStatusIndex({
                        stageId: sourceData.stageId,
                        startIndex: sourceData.index,
                        indexOfTarget,
                        closestEdgeOfTarget,
                    });
                }
            },
        });
    }, [flowList, getStagesIndex, getStatusIndex]);

    /**
     * 构建完整的渲染项列表
     * @returns 包含阶段和添加按钮的渲染项数组
     */
    const renderItems: ProcessStageItemComponentProps[] = useMemo(() => {
        const items = [];

        const pendingList = flowList.filter((item: any) => item?.stageDict?.type !== "5");

        const finalList = flowList.filter((item: any) => item?.stageDict?.type === "5");

        // 添加过程阶段
        pendingList.forEach((stage: any, index: number) => {
            items.push({
                stage: {
                    type: "pending",
                    draggable: true,
                    deletable: true,
                    data: stage,
                    label: `第${index + 1}阶段`,
                },
                index,
                readonly,
            });
        });

        // 添加"添加阶段"按钮
        if (!readonly) {
            items.push({
                stage: {
                    type: "add",
                    draggable: false,
                    deletable: false,
                    index: flowList.length,
                },
                index: flowList.length,
                readonly,
            });
        }

        // 添加最终阶段
        finalList.forEach((stage: any, index: number) => {
            items.push({
                stage: {
                    type: "final",
                    draggable: false,
                    deletable: false,
                    data: stage,
                    label: "最终阶段",
                },
                index: flowList.length + 1 + index,
                readonly,
            });
        });

        return items;
    }, [flowList]);

    return (
        <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
            {renderItems.map((item, index) => (
                <ProcessStageItem
                    key={item.stage.data?.id || `${item.stage.type}-${index}`}
                    stage={item.stage}
                    index={index}
                    readonly={item.readonly}
                />
            ))}
        </div>
    );
};

export default RecruitmentProcess;
