"use client";

import React, { useState, useCallback, useRef, useMemo, useEffect } from "react";
import styles from "./index.module.scss";
import ExpandIcon from "@/app/icons/dashboard/expand_icon.svg";
import CollapseIcon from "@/app/icons/dashboard/collapse_icon.svg";
import clsx from "clsx";
import { PlusOutlined } from "@ant-design/icons";
import { draggable, dropTargetForElements } from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { setCustomNativeDragPreview } from "@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview";
import { combine } from "@atlaskit/pragmatic-drag-and-drop/combine";
import { DropIndicator } from "@atlaskit/pragmatic-drag-and-drop-react-drop-indicator/box";
import { pointerOutsideOfPreview } from "@atlaskit/pragmatic-drag-and-drop/element/pointer-outside-of-preview";
import StageChild from "../StageChild";
import DraggableItem from "../DraggableItem";
import PlusPopover from "../PlusPopover";
import { Button, Checkbox } from "antd";
import ChatDialog from "@/app/components/ChatDialog";
import UserSelect from "@/app/components/UserSelect";
import { useFlowActions } from "../../../../../hooks/useFlowActions";
import useJobDetailStore from "@/app/store/modules/dashboard/jobDetail";
import BindInterviewTemplate from "./BindInterviewTemplate";

import {
    getStageItemData,
    isDragData,
    attachClosestEdge,
    extractClosestEdge,
    idleState,
    draggingState,
    type DraggableState,
    type Edge,
} from "../../dragUtils";
import { createPortal } from "react-dom";

/**
 * 流程阶段项组件属性接口
 */
interface ProcessStageItemComponentProps {
    stage: {
        type: string;
        draggable: boolean;
        deletable: boolean;
        data?: any;
        label?: string;
        index?: number;
    };
    index?: number;
    readonly?: boolean;
}

/**
 * 流程阶段项组件
 * @param props 组件属性
 * @returns 阶段项组件
 */
const ProcessStageItem: React.FC<ProcessStageItemComponentProps> = ({ stage, index = 0, readonly = false }) => {
    const { data, draggable: isDraggable, type } = stage;
    const [isExpanded, setIsExpanded] = useState(false);

    const ref = useRef<HTMLDivElement>(null);
    const dragHandleRef = useRef<HTMLDivElement>(null);
    const [draggableState, setDraggableState] = useState<DraggableState>(idleState);
    const [closestEdge, setClosestEdge] = useState<Edge | null>(null);

    /**
     * 切换展开/收起状态
     */
    const toggleExpanded = useCallback(() => {
        setIsExpanded((prev) => !prev);
    }, []);

    useEffect(() => {
        const element = ref.current;
        const dragHandle = dragHandleRef.current;
        if (!element || !dragHandle || !isDraggable || readonly || type === "add" || type === "final") return;

        const dragData = getStageItemData({ item: data, index });

        return combine(
            draggable({
                element: dragHandle,
                getInitialData: () => dragData,
                onGenerateDragPreview({ nativeSetDragImage }) {
                    setCustomNativeDragPreview({
                        nativeSetDragImage,
                        getOffset: pointerOutsideOfPreview({
                            x: "16px",
                            y: "8px",
                        }),
                        render({ container }) {
                            setDraggableState({ type: "preview", container });
                            return () => setDraggableState(draggingState);
                        },
                    });
                },
                onDragStart() {
                    setDraggableState(draggingState);
                },
                onDrop() {
                    setDraggableState(idleState);
                },
            }),
            dropTargetForElements({
                element,
                canDrop({ source }) {
                    return isDragData(source.data) && source.data.type === "stage";
                },
                getData({ input }) {
                    return attachClosestEdge(dragData, {
                        element,
                        input,
                        allowedEdges: ["top", "bottom"],
                    });
                },
                getIsSticky() {
                    return true;
                },
                onDragEnter({ source, self }) {
                    if (source.element === dragHandle) {
                        setClosestEdge(null);
                        return;
                    }
                    const edge = extractClosestEdge(self.data);
                    setClosestEdge(edge);
                },
                onDrag({ source, self }) {
                    if (source.element === dragHandle) {
                        setClosestEdge(null);
                        return;
                    }
                    const edge = extractClosestEdge(self.data);
                    setClosestEdge(edge);
                },
                onDragLeave() {
                    setClosestEdge(null);
                },
                onDrop() {
                    setClosestEdge(null);
                },
            })
        );
    }, [data, index, isDraggable, readonly, type]);

    // 如果是添加按钮或最终阶段，不需要拖拽
    if (type === "add" || type === "final") {
        return (
            <div className={styles["process-item"]}>
                {type === "add" ? (
                    <AddItem />
                ) : (
                    <StageItem
                        stage={stage}
                        toggleExpanded={toggleExpanded}
                        isExpanded={isExpanded}
                        readonly={readonly}
                    />
                )}
            </div>
        );
    }

    // 可拖拽的阶段项
    return (
        <>
            <div
                ref={ref}
                className={clsx(
                    styles["process-item"],
                    draggableState.type === "dragging" && styles["process-item-dragging"]
                )}
                style={{
                    // opacity: draggableState === "dragging" ? 0.5 : 1,
                    cursor: draggableState.type === "dragging" ? "grabbing" : "default",
                }}
            >
                <StageItem
                    stage={stage}
                    toggleExpanded={toggleExpanded}
                    isExpanded={isExpanded}
                    dragHandleRef={dragHandleRef}
                    readonly={readonly}
                />
                {closestEdge && <DropIndicator edge={closestEdge} gap="24px" />}
            </div>
            {draggableState.type === "preview" &&
                createPortal(<DragPreview stage={stage.data} />, draggableState.container)}
        </>
    );
};

const AddItem = () => {
    const { stageList, flowDetail } = useJobDetailStore();
    const { addStage } = useFlowActions();

    const noSelectedStageList = useMemo(() => {
        const selectedStageCodeList = flowDetail?.stage?.map((item) => item.dictId);
        return stageList.filter((item) => !selectedStageCodeList?.includes(item.code));
    }, [stageList, flowDetail]);

    const handleStatusSelect = (item: { id: string; code: string; name: string }) => {
        addStage({
            dictId: item.code,
            handlers: [],
            handlersEntity: [],
            order: null,
            stageDict: item,
            states: [],
        });
    };

    const handleCreateItem = () => {
        console.log("新建状态");
    };
    return (
        <PlusPopover
            createItemLabel="新建并选择阶段"
            list={noSelectedStageList}
            onSelect={handleStatusSelect}
            onCreateItem={handleCreateItem}
            creatable={false}
        >
            <div className={clsx(styles["process-item-content"], styles["process-item-content-add"])}>
                <div className={styles["process-stage-item-add"]}>
                    <PlusOutlined />
                    <div className={styles["process-item-content-add-text"]}>新增阶段</div>
                </div>
            </div>
        </PlusPopover>
    );
};

interface StageItemProps {
    stage: ProcessStageItemComponentProps["stage"];
    toggleExpanded: () => void;
    isExpanded: boolean;
    readonly?: boolean;
    dragHandleRef?: React.RefObject<HTMLDivElement>;
}
const StageItem: React.FC<StageItemProps> = ({
    stage,
    toggleExpanded,
    isExpanded,
    readonly = false,
    dragHandleRef,
}) => {
    const { data = {}, deletable, draggable, label } = stage;
    const { deleteStage, updateStage } = useFlowActions();

    const { stageDict = {} } = data;

    const { detailInfo } = useJobDetailStore((state) => ({
        detailInfo: state.detailInfo,
    }));

    /**
     * 删除阶段
     */
    const handleDelete = useCallback(() => {
        deleteStage(data.dictId);
    }, [data]);

    /**
     * 面试模板变更
     */
    const handleTemplateChange = (value: string | undefined) => {
        updateStage(data.dictId, {
            reviewTemplate: value,
        });
    };

    return (
        <>
            <div className={styles["process-item-title"]}>{label}</div>
            <div className={styles["process-item-content"]}>
                <div className={styles["process-stage-item"]}>
                    <DraggableItem
                        draggable={draggable}
                        deletable={deletable}
                        readonly={readonly}
                        handleDelete={handleDelete}
                        dragHandleRef={dragHandleRef}
                    >
                        <div className={styles["stage-content"]}>
                            <div className={styles["stage-content-title"]}>
                                {stageDict.name}
                                <span className={styles["stage-content-code"]}>({stageDict.code})</span>
                            </div>
                            <div className={styles["process-stage-item-header-right"]}>
                                <div className={styles["process-stage-item-check-status"]} onClick={toggleExpanded}>
                                    查看阶段下状态
                                    {isExpanded ? <CollapseIcon /> : <ExpandIcon />}
                                </div>
                                <div
                                    className={
                                        stageDict.type == "2"
                                            ? styles["middle-line"]
                                            : styles["muddle-line-BindInterviewTemplate"]
                                    }
                                >
                                    <div className={stageDict.type == "2" ? styles["not-only-assocInterviewer"] : ""}>
                                        <AssocInterviewer data={data} readonly={readonly} />
                                    </div>
                                    {stageDict.type == "2" && (
                                        <BindInterviewTemplate
                                            data={data}
                                            orgId={detailInfo?.orgId}
                                            onTemplateChange={handleTemplateChange}
                                            readonly={readonly}
                                        />
                                    )}
                                </div>
                            </div>
                        </div>
                    </DraggableItem>
                    {isExpanded && (
                        <StageChild stage={stage.data} subStatuses={data?.states || []} readonly={readonly} />
                    )}
                </div>
            </div>
        </>
    );
};

export default ProcessStageItem;

// 关联面试官
const AssocInterviewer = ({ data, readonly }: { data: any; readonly: boolean }) => {
    const [showUserSelect, setShowUserSelect] = useState(false);
    const { updateStage } = useFlowActions();

    const userSelectRef = useRef<any>(null);
    const handlersEntity = useMemo(() => {
        return (
            data?.handlersEntity?.map((item: any) => ({
                empId: item.empId,
                empName: item.empName,

                // deptCode: item.deptCode,
                // deptName: item.deptName,
            })) || []
        );
    }, [data?.handlersEntity]);

    const isChecked = handlersEntity.length > 0;

    /**
     * 根据阶段类型获取面试官显示文本
     * @param handlersEntity 面试官列表
     * @param stageType 阶段类型
     * @returns 显示文本
     */
    const getInterviewerDisplayText = (handlersEntity: any[], stageType: string) => {
        if (!handlersEntity || handlersEntity.length === 0) {
            return "关联面试官";
        }

        // 根据阶段类型确定最大显示数量
        const maxDisplayCount = stageType === "2" ? 4 : 8;

        if (handlersEntity.length > maxDisplayCount) {
            // 超过最大显示数量时，显示前N个面试官和总数
            const displayInterviewers = handlersEntity
                .slice(0, maxDisplayCount)
                .map((item: any) => `${item.empName}`)
                .join("、");
            return `${displayInterviewers}...等${handlersEntity.length}人`;
        } else {
            // 未超过最大显示数量时，显示所有面试官
            return handlersEntity.map((item: any) => `${item.empName}`).join("、");
        }
    };

    const handleSelectUser = () => {
        const selectedUsers = userSelectRef.current.getSelectedUser;
        updateStage(data.dictId, {
            handlersEntity: selectedUsers || [],
            handlers: selectedUsers?.map((item: any) => item.empId),
        });
        setShowUserSelect(false);
    };

    // 判断是否超过最大显示数量
    const maxDisplayCount = data.stageDict.type === "2" ? 4 : 8;
    const isOverMaxDisplay = handlersEntity.length > maxDisplayCount;

    // 只读模式下的点击处理
    const handleReadonlyClick = () => {
        if (!readonly) return;
        // 只有绑定了面试官且超过最大显示数量时才显示弹窗
        if (isChecked && isOverMaxDisplay) {
            setShowUserSelect(true);
        }
    };

    return (
        <div style={{ width: "100%" }}>
            <Checkbox
                style={{
                    fontSize: 12,
                    width: "100%",
                    color: isChecked ? "#3168f1" : "",
                    cursor: readonly ? (isChecked && isOverMaxDisplay ? "pointer" : "default") : "pointer",
                }}
                checked={isChecked}
                disabled={readonly && (!isChecked || !isOverMaxDisplay)}
                onChange={() => {
                    if (readonly) {
                        handleReadonlyClick();
                        return;
                    }
                    setShowUserSelect(true);
                    const timer = setTimeout(() => {
                        userSelectRef.current?.tableRef?.reset?.();
                        userSelectRef.current?.setSelectedUser?.(handlersEntity);
                        clearTimeout(timer);
                    }, 100);
                }}
                onClick={readonly ? handleReadonlyClick : undefined}
            >
                {getInterviewerDisplayText(handlersEntity, data.stageDict.type)}
            </Checkbox>
            <ChatDialog
                open={showUserSelect}
                title={readonly ? "查看面试官" : "选择面试官"}
                width="75%"
                onOk={readonly ? () => setShowUserSelect(false) : handleSelectUser}
                onCancel={() => setShowUserSelect(false)}
                confirmLoading={false}
                footer={
                    readonly
                        ? [
                              <Button key="close" onClick={() => setShowUserSelect(false)}>
                                  关闭
                              </Button>,
                          ]
                        : undefined
                }
            >
                {readonly ? (
                    <div style={{ padding: "16px 0" }}>
                        <div style={{ marginBottom: "12px", fontWeight: "bold" }}>面试官列表：</div>
                        {handlersEntity.map((item: any, index: any) => (
                            <div key={item.empId} style={{ padding: "4px 0" }}>
                                {index + 1}. {item.empName}({item.empId})
                            </div>
                        ))}
                    </div>
                ) : (
                    <UserSelect mode="checkbox" ref={userSelectRef} />
                )}
            </ChatDialog>
        </div>
    );
};

function DragPreview({ stage }: { stage: any }) {
    return <div className={styles["drag-preview"]}>{stage.stageDict?.name}</div>;
}
