.process-item {
    display: flex;
    width: 100%;
    position: relative;
    --process-gap: 24px;

    .process-stage-item-delete {
        cursor: pointer;
        position: absolute;
        top: 13px;
        right: 3px;
        color: #a6aaad;
        &:hover {
            color: #146eff;
        }
    }

    .process-item-title {
        padding-bottom: var(--process-gap);
        position: absolute;
        color: #3e4e59;
        font-size: 12px;
        line-height: 20px;
        top: 10px;
    }

    .process-item-content {
        flex: 1;
        margin-left: 74px;
        padding-bottom: var(--process-gap);
        margin-right: 30px;

        &::before {
            content: "";
            position: absolute;
            left: 60px;
            top: 0;
            width: 1px;
            background-color: #1890ff;
            // 默认高度为完整高度
            height: 100%;
        }

        &::after {
            content: "";
            position: absolute;
            left: 58px;
            top: 16px;
            width: 5px;
            height: 5px;
            background-color: #1890ff;
            border-radius: 50%;
        }
    }
}

.process-item {
    &:first-child {
        .process-item-content {
            &::before {
                top: 17px;
            }
        }
    }

    &:last-child {
        .process-item-content {
            &::before {
                height: 18px;
            }
        }
    }
}

.process-stage-item {
    border: 1px solid rgba(240, 242, 245, 0.7);
    border-radius: 5px;

    .stage-content {
        display: flex;
        flex: 1;
        justify-content: space-between;
        align-items: center;

        .stage-content-title {
            font-size: 14px;
            color: #33383d;
            font-weight: 700;
        }
    }

    .process-stage-item-header {
        background-color: #f6f7f8;
        height: 40px;
        display: flex;
        align-items: center;
        flex: 1;
        padding-right: 16px;

        .process-stage-item-header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        .process-stage-item-header-right {
            display: flex;
            align-items: center;
            gap: 16px;

            .process-stage-item-check-status {
                cursor: pointer;
                font-size: 12px;
                line-height: 40px;
                color: #33383d;
                display: flex;
                align-items: center;
                gap: 8px;
                &:hover {
                    color: #146eff;
                }
            }
        }
    }
}

.process-item-content-add {
    cursor: pointer;
    &::after {
        display: none;
    }

    .process-stage-item-add {
        border: 1px dashed #80b7ff;
        border-radius: 5px;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 16px;
        color: #0054f0;
        gap: 16px;

        &:hover {
            background-color: #f3f8ff;
        }
    }
}

.child-content {
    .child-content-header {
        font-size: 12px;
        line-height: 20px;
        height: 20px;
        color: #565e66;
        padding: 24px 58px 4px 32px;
    }
}

// 确保拖拽时元素保持原位
.process-item {
    // 禁用默认的拖拽动画
    &[data-rbd-draggable-context-id] {
        transform: none !important;
        transition: none !important;
    }
}
.process-item-dragging {
    .process-item-content {
        // opacity: 0.5;

        background-color: #f3f8ff;
        border: 1px solid #91c9ff;
        border-radius: 6px;
    }
}

.process-item-placeholder {
    opacity: 0.5;
    // background-color: #f0f8ff; // 一个淡淡的背景色以示区别

    .process-item-title {
        display: none;
    }

    .process-item-content {
        background-color: #f0f8ff;
        border: 1px solid #1890ff;
        border-radius: 6px;
        height: 40px;
        .process-stage-item-delete {
            display: none;
        }

        .process-stage-item-content {
            display: none;
        }
        &::before {
            display: none;
        }
        &::after {
            display: none;
        }
    }

    .process-stage-item-add {
        &:hover {
            background-color: white !important;
        }
    }
}

// 拖拽手柄样式优化
// .process-stage-item-header-left {
//     display: flex;
//     align-items: center;
//     gap: 16px;
// }

// 拖拽占位符样式
// .process-item + .process-item {
//     margin-top: 0;
// }

// 插入指示器样式
// .drop-indicator {
//     height: 2px;
//     background: #1890ff;
//     margin: 8px 0;
//     border-radius: 1px;
//     opacity: 0;
//     transition: opacity 0.2s ease;
//     position: relative;

//     &.active {
//         opacity: 1;
//     }

//     // 添加发光效果
//     &::before {
//         content: "";
//         position: absolute;
//         top: -1px;
//         left: 0;
//         right: 0;
//         height: 4px;
//         background: rgba(24, 144, 255, 0.3);
//         border-radius: 2px;
//         opacity: 0;
//         transition: opacity 0.2s ease;
//     }

//     &.active::before {
//         opacity: 1;
//     }
// }
