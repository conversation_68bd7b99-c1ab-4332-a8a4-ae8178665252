.child-container {
    .child-container-header {
        font-size: 12px;
        line-height: 20px;
        color: #565e66;
        padding: 24px 58px 4px 32px;
        display: flex;
        gap: 16px;

        .code-label {
            width: 40px;
            margin-left: 47px;
        }
        .name-label {
            width: 72px;
        }
        .alias-label {
            width: 118px;
        }
        .reason-label {
            width: 133px;
        }
        .skill-label {
            width: 118px;
        }
    }

    .child-container-content {
        padding: 0 58px 24px 32px;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .draggable-content {
            border-radius: 4px;
            border: 1px solid rgba(191, 195, 199, 0.2);
            background-color: #fafbfb;
        }
        .draggable-content-dragging {
            border: 1px solid #91c9ff;
        }

        .child-container-content-item {
            font-size: 12px;
            padding: 7px 0;
            display: flex;
            align-items: center;
            gap: 16px;

            .code-value {
                width: 40px;
            }
            .name-value {
                width: 72px;
                font-weight: 700;
            }
            .alias-value {
                width: 118px;

                :global(.ant-input) {
                    padding: 2px 4px;
                    font-size: 12px;
                }
            }
            .reason-value {
                width: 133px;
            }
            .skill-value {
                width: 118px;
            }
        }

        .child-gap {
            height: 10px;
            width: 100%;
        }
    }
}

.popover-footer {
    display: flex;
    justify-content: flex-end;
    padding: 12px 16px;
    border-top: 1px solid rgba(191, 195, 199, 0.2);
    padding: 0;
    padding-top: 10px;
}

.plus-btn {
    color: #2a61f1;
    border: 1px solid #80b9ff;
}

.drag-preview {
    border-radius: 4px;
    padding: 8px;
    background-color: white;
}
