import { getPathValue } from "@/app/utils";
import styles from "./index.module.scss";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import RecruitmentProcess from "./RecruitmentProcess";
import { Button, Layout, message } from "antd";
import useJobDetailStore, { JobDetailExpose, BatchJobResp } from "@/app/store/modules/dashboard/jobDetail";
import { MyFlowResp } from "@/app/store/modules/dashboard/jobFlow";
import { saveInterviewFlowApi, saveBatchInterviewFlowApi } from "@/app/request/modules/dashboard";

const Detail = forwardRef<
    JobDetailExpose,
    { detailInfo: MyFlowResp | null; ids?: number[]; onCancel?: () => void; onOk?: () => void }
>((props, ref) => {
    const { detailInfo, onCancel, onOk } = props;

    // 存储类型
    const [batchJobList, setBatchJobList] = useState<BatchJobResp[]>([]);
    const [operationType, setOperationType] = useState<string>("");

    const {
        flowDetail,
        resetFlowDetail,
        getFlowDetail,
        getStageList,
        getStatusList,
        getFinalFlowDetail,
        setDetailInfo,
    } = useJobDetailStore((state) => ({
        flowDetail: state.flowDetail,
        getFlowDetail: state.getFlowDetail,
        getStageList: state.getStageList,
        getStatusList: state.getStatusList,
        resetFlowDetail: state.resetFlowDetail,
        getFinalFlowDetail: state.getFinalFlowDetail,
        setDetailInfo: state.setDetailInfo,
    }));

    useImperativeHandle(
        ref,
        () => ({
            init: (batchJobList?: BatchJobResp[], type?: string) => {
                // 保存数据和类型
                if (batchJobList) {
                    setBatchJobList(batchJobList);
                }
                if (type) {
                    setOperationType(type);
                }
            },
        }),
        []
    );

    useEffect(() => {
        if (!detailInfo?.id) {
            return;
        }
        setDetailInfo(detailInfo);
        getFlowDetail(detailInfo?.id);
    }, [detailInfo?.id]);

    useEffect(() => {
        getStageList();
        getStatusList();
    }, []);

    const handleSubmit = async () => {
        const finalFlowDetail = getFinalFlowDetail();

        let response;
        if (operationType === "batch") {
            // 批量保存 - 去掉外层id和每个stage里的id
            const cleanedFlowDetail = {
                ...finalFlowDetail,
                stage: finalFlowDetail?.stage?.map((stage) => {
                    const { id, ...stageWithoutId } = stage;
                    return {
                        ...stageWithoutId,
                        states: stage.states?.map((state) => {
                            const { id, ...stateWithoutId } = state;
                            return stateWithoutId;
                        }),
                    };
                }),
            };
            // 删除外层的id
            delete cleanedFlowDetail.id;
            response = await saveBatchInterviewFlowApi({
                ids: batchJobList.map((item) => item.jobSpecId),
                ...cleanedFlowDetail,
            });
        } else {
            // 单个保存
            response = await saveInterviewFlowApi(finalFlowDetail);
        }

        const result: any = await response.json();
        if (result.code === 200) {
            message.success("保存成功");
            resetFlowDetail();
            onOk?.();
        }
    };

    const handleCancel = () => {
        onCancel?.();
    };

    return (
        <Layout style={{ height: "100%" }}>
            <Layout.Content style={{ overflow: "auto" }}>
                <div className={styles["job-manage-detail"]}>
                    <div className={styles["job-manage-detail__info"]}>
                        <div className={styles["job-manage-detail__title"]}>
                            {getPathValue(detailInfo?.jobNameInner ?? "")}
                        </div>
                        <div className={styles["job-manage-detail__content"]}>
                            需求部门：{getPathValue(detailInfo?.orgName ?? "")}
                        </div>
                        <div className={styles["job-manage-detail__content"]}>
                            招聘负责人：
                            {detailInfo?.createUser?.empId
                                ? `${detailInfo?.createUser?.empId}-${detailInfo?.createUser?.empName}`
                                : ""}
                        </div>
                    </div>
                    <div className={styles["job-manage-detail__interview"]}>
                        <div className={styles["job-manage-detail__title"]}>面试流程配置</div>
                        <RecruitmentProcess flowList={flowDetail?.stage} />
                    </div>
                </div>
            </Layout.Content>
            <Layout.Footer style={{ padding: 0, backgroundColor: "white" }}>
                <div className={styles["job-manage-detail__footer"]}>
                    <Button onClick={handleCancel}>取消</Button>
                    <Button type="primary" onClick={handleSubmit}>
                        确认
                    </Button>
                </div>
            </Layout.Footer>
        </Layout>
    );
});
Detail.displayName = "JobDetail";

export default Detail;
