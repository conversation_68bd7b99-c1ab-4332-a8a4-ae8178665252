import RenderFile from "@/app/components/RenderFile";
import { getBeisenEvaluationReportApi } from "@/app/request/modules/candidate";
import { getDictDataByCodeApi } from "@/app/request/modules/common";
import { DictData, RespParams } from "@/app/typing";
import { Empty, Flex, Form, Select } from "antd";
import { useEffect, useState } from "react";

const EvaluationReport = ({
    applicantId,
    jobId,
    profileName,
}: {
    applicantId: string;
    jobId: string;
    profileName: string;
}) => {
    const [evaluationReportBlob, setEvaluationReportBlob] = useState<Blob>();
    const [evaluationType, setEvaluationType] = useState<DictData[]>([]);
    const [currentEvaluationType, setCurrentEvaluationType] = useState<string>("");

    // 获取测评报告
    const getEvaluationReport = async () => {
        if (!applicantId || !jobId || !currentEvaluationType) return;

        try {
            const res: Blob = await getBeisenEvaluationReportApi({
                jobId: jobId,
                applicantId: applicantId,
                reportName: currentEvaluationType,
            });
            if (res.type === "application/pdf") {
                setEvaluationReportBlob(res);
                return;
            }
            setEvaluationReportBlob(undefined);
        } catch (err) {
            setEvaluationReportBlob(undefined);
        }
    };
    // 获取测评报告类型
    const getEvaluationType = async () => {
        try {
            const res: RespParams<DictData[]> = await getDictDataByCodeApi("TalentAssessmentReport").json();
            if (res.code === 200) {
                setEvaluationType(res.data);
                setCurrentEvaluationType(res.data?.[0].value ?? "");
            }
        } catch (err) {
            setEvaluationType([]);
        }
    };

    useEffect(() => {
        getEvaluationType();
    }, []);
    useEffect(() => {
        if (currentEvaluationType) {
            getEvaluationReport();
        }
    }, [currentEvaluationType]);

    return (
        <Flex vertical>
            <Form>
                <Form.Item label="测评报告类型">
                    <Select
                        value={currentEvaluationType}
                        placeholder="请选择测评报告类型"
                        options={evaluationType}
                        popupMatchSelectWidth={false}
                        onChange={(value) => setCurrentEvaluationType(value)}
                    />
                </Form.Item>
            </Form>
            {evaluationReportBlob ? (
                <RenderFile blob={evaluationReportBlob} fileName={`${profileName ?? ""}-测评报告`} />
            ) : (
                <Empty />
            )}
        </Flex>
    );
};

export default EvaluationReport;
