.interview-transcript-wrapper {
    display: flex;
    flex-direction: column;
}

.header-btn-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    :global {
        .ant-btn {
            padding: 0 8px !important;
        }
        .ant-divider-vertical {
            margin-inline: 4px !important;
        }
    }
}

.description-item {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
    margin-top: 4px;
    margin-bottom: 16px;
    border: 1px solid #eeeeee;

    .description-item-top {
        display: flex;
        padding: 16px;
        flex-direction: column;
        background-color: #f8f9fb;
        gap: 12px;
    }
    
    .item-top-content {
        display: grid;
        grid-template-columns: 60px 1fr 60px 1fr;
        gap: 8px;
        align-items: center;
        font-size: 12px;
    }

    .description-item-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .description-item-header-title {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 500;
            color: #000000;
        }

        .interview-type {
            border: 1px solid #d4d4d4;
            font-size: 12px;
            font-weight: 400;
            color: #757575;
            gap: 4px;
            border-radius: 4px;
            padding: 4px;
            margin-left: 8px;
            display: flex;
            align-items: center;
        }

        .description-item-header-select {
            display: flex;
            align-items: center;
            gap: 16px;
        }
    }

    .description-item-bottom {
        display: flex;
        padding: 16px;
        flex-direction: column;
        gap: 12px;

        .bottom-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
            font-weight: 400;
        }
    }
}