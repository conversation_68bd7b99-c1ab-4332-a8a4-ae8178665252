import ChatDrawer from "@/app/components/ChatDrawer";
import messageService from "@/app/lib/message";
import { getInterviewFlowApi, getSubJobListApi, resumeChangeStatusApi } from "@/app/request/modules/candidate";
import { getDepartmentListApi } from "@/app/request/modules/common";
import {
    AddJobExpose,
    InterviewFlowResp,
    JobResp,
    ResumeChangeStatusReq,
    TrackStageStatistic,
} from "@/app/store/modules/candidate";
import { Department } from "@/app/store/modules/dashboard/jobFlow";
import { RespParams } from "@/app/typing";
import { Flex, Form, Input, Select } from "antd";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";

const AddJob = forwardRef<AddJobExpose, { refresh: () => void }>((props, ref) => {
    const [show, setShow] = useState<boolean>(false);
    const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
    const [stageName, setStageName] = useState<string>();
    const [applicantId, setApplicantId] = useState<string>("");
    const [jobList, setJobList] = useState<JobResp[]>([]);
    const [currentDept, setCurrentDept] = useState<Department>();
    const [originJobName, setOriginJobName] = useState<string>("");
    const [statusList, setStatusList] = useState<TrackStageStatistic[]>([]);

    const [form] = Form.useForm();
    const { refresh } = props;

    const getTreeData = async () => {
        const res: RespParams<Department> = await (await getDepartmentListApi()).json();
        if (res.code === 200) {
            setCurrentDept(res.data);
        }
    };
    useEffect(() => {
        if (currentDept) {
            getJobList();
        }
    }, [currentDept]);

    const getJobList = async () => {
        setJobList([]);
        const res: RespParams<JobResp[]> = await (await getSubJobListApi(currentDept?.dept_code ?? "")).json();
        if (res.code === 200) {
            setJobList(res?.data ?? []);
        }
    };

    // 获取面试流程列表
    const getInterviewFlowList = async (jobId: number) => {
        try {
            const res: RespParams<InterviewFlowResp> = await (await getInterviewFlowApi(jobId)).json();
            if (res.code === 200) {
                const list: TrackStageStatistic[] = res.data.trackStageStatistics;
                setStatusList(list);
            }
        } catch (error) {
            setStatusList([]);
        }
    };

    useImperativeHandle(ref, () => {
        return {
            showDrawer: (applicantId: string, stageName: string, originJobName: string) => {
                setShow(true);
                getTreeData();
                setStageName(stageName);
                setApplicantId(applicantId);
                setOriginJobName(originJobName);
                form.resetFields();
            },
        };
    });

    const handleOk = async () => {
        const validateRes = await form.validateFields();
        if (validateRes) {
            const formValue = form.getFieldsValue();
            const currentStage = statusList.find((item) => item.stageCode.includes("S0001"));
            const currentState = currentStage?.trackStateStatistics.find((item) => item.stateCode.includes("U0007"));

            if (!currentStage?.stageId || !currentState?.stateId) {
                return;
            }

            const params: any = {
                applicantId: applicantId,
                jobId: formValue.jobId,
                stageId: currentStage.stageId,
                stateId: currentState.stateId,
            };

            try {
                // setConfirmLoading(true);
                // const res: RespParams<any> = await (await resumeChangeStatusApi(params)).json();
                // if (res.code === 200) {
                //     messageService.success(res?.msg ?? "状态变更成功");
                //     setConfirmLoading(false);
                //     setShow(false);
                //     refresh();
                // }
            } catch (err) {
                setConfirmLoading(false);
            }
        }
    };

    return (
        <ChatDrawer
            title="加入职位"
            open={show}
            confirmLoading={confirmLoading}
            onOk={handleOk}
            onCancel={() => setShow(false)}
        >
            <Form layout="vertical" form={form}>
                <Form.Item label="当前状态">
                    <Input value={stageName} disabled allowClear />
                </Form.Item>
                <Form.Item label="当前职位">
                    <Input value={originJobName} disabled allowClear />
                </Form.Item>
                <Form.Item name="jobId" label="加入后职位" rules={[{ required: true }]}>
                    <Select
                        options={jobList}
                        placeholder="请选择职位"
                        showSearch
                        fieldNames={{ label: "jobNameInner", value: "id" }}
                        optionRender={(option) => {
                            return (
                                <Flex vertical>
                                    <span>{option.data.jobNameInner}</span>
                                    <span style={{ color: "var(--sub-text-color)" }}>{option.data.orgName}</span>
                                </Flex>
                            );
                        }}
                        onChange={() => getInterviewFlowList(form.getFieldValue("jobId"))}
                    />
                </Form.Item>
            </Form>
        </ChatDrawer>
    );
});
AddJob.displayName = "AddJob";

export default AddJob;
