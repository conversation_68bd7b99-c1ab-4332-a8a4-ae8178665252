/* eslint-disable @next/next/no-page-custom-font */

import "@/styles/globals.scss";
import "@/styles/markdown.scss";
import "@/styles/highlight.scss";
import "@/styles/format.scss";

import { getClientConfig } from "@/config/client";
import type { Metadata, Viewport } from "next";
// import { SpeedInsights } from "@vercel/speed-insights/next";
// import { GoogleTagManager, GoogleAnalytics } from "@next/third-parties/google";
// import { getServerSideConfig } from "./config/server";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { App, ConfigProvider } from "antd";
import zhCN from "antd/locale/zh_CN";
import { ClientOnlyWatermark } from "@/components/WaterMark";
import ClientConfigProvider from "@/components/ClientConfigProvider";

export const metadata: Metadata = {
    title: "InovanceHRChat",
    description: "汇川招聘智能助手",
    appleWebApp: { title: "InovanceHRChat", statusBarStyle: "default" },
};

export const viewport: Viewport = {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
    // minimumScale: 1,
    // userScalable: false,
    themeColor: [
        { media: "(prefers-color-scheme: light)", color: "#fafafa" },
        { media: "(prefers-color-scheme: dark)", color: "#151515" },
    ],
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
    // const serverConfig = getServerSideConfig();

    return (
        <html lang="en">
            <head>
                <meta name="config" content={JSON.stringify(getClientConfig())} />
                <meta
                    name="viewport"
                    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
                />
                <link rel="manifest" href="/site.webmanifest" crossOrigin="use-credentials"></link>
                <script src="/serviceWorkerRegister.js" defer></script>
            </head>
            <body>
                <ClientConfigProvider>
                    <AntdRegistry>
                        <App style={{ width: "100vw", height: "100vh" }}>
                            <ClientOnlyWatermark>{children}</ClientOnlyWatermark>
                        </App>
                    </AntdRegistry>
                </ClientConfigProvider>
                {/* {serverConfig?.isVercel && (
                    <>
                        <SpeedInsights />
                    </>
                )}
                {serverConfig?.gtmId && (
                    <>
                        <GoogleTagManager gtmId={serverConfig.gtmId} />
                    </>
                )}
                {serverConfig?.gaId && (
                    <>
                        <GoogleAnalytics gaId={serverConfig.gaId} />
                    </>
                )} */}
            </body>
        </html>
    );
}
