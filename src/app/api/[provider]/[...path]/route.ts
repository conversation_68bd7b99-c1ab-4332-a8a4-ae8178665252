import { ApiPath } from "@/app/constant";
import { NextRequest } from "next/server";
import { handle as deepseek<PERSON><PERSON><PERSON> } from "@/deepseek";
import { handle as proxy<PERSON>andler } from "@/proxy";

async function handle(req: NextRequest, { params }: { params: { provider: string; path: string[] } }) {
    const apiPath = `/api/${params.provider}`;
    console.log(`[${params.provider} Route] params `, params);
    switch (apiPath) {
        case ApiPath.DeepSeek:
            return deepseekHandler(req, { params });
        default:
            return proxyHandler(req, { params });
    }
}

export const GET = handle;
export const POST = handle;

export const runtime = "edge";
export const preferredRegion = [
    "arn1",
    "bom1",
    "cdg1",
    "cle1",
    "cpt1",
    "dub1",
    "fra1",
    "gru1",
    "hnd1",
    "iad1",
    "icn1",
    "kix1",
    "lhr1",
    "pdx1",
    "sfo1",
    "sin1",
    "syd1",
];
