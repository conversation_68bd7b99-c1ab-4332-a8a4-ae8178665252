import { readFileSync } from "fs";
import { join } from "path";
import { NextResponse } from "next/server";

export async function GET() {
    try {
        // 读取服务器端的 BUILD_ID
        const buildIdPath = join(process.cwd(), ".next", "BUILD_ID");
        const serverBuildId = readFileSync(buildIdPath, "utf8").trim();
        return NextResponse.json({ buildId: serverBuildId });
    } catch (error) {
        console.error("读取版本失败:", error);
        return NextResponse.json({ error: "获取版本失败" }, { status: 500 });
    }
}
