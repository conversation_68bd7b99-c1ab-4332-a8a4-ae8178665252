import { NextRequest, NextResponse } from "next/server";

/**
 * 代理转发聊天请求，解决CORS问题
 */
export async function POST(request: NextRequest) {
    try {
        // 获取原始请求数据
        const body = await request.json();

        // 转发请求到目标API
        const response = await fetch(`http://10.32.229.138:7777/api/v1alpha1/chat`, {
            method: "POST",
            headers: {
                Accept: "text/event-stream",
                "Content-Type": "application/json",
                Connection: "keep-alive",
            },
            body: JSON.stringify(body),
        });

        // 检查响应状态
        if (!response.ok) {
            return NextResponse.json({ error: `请求目标API失败: ${response.status}` }, { status: response.status });
        }

        // 直接返回响应流
        return new NextResponse(response.body, {
            status: response.status,
            headers: {
                "Content-Type": "text/event-stream",
                "Cache-Control": "no-cache",
                Connection: "keep-alive",
            },
        });
    } catch (error: any) {
        console.error("代理请求错误:", error);
        return NextResponse.json({ error: `代理请求处理错误: ${error.message}` }, { status: 500 });
    }
}
