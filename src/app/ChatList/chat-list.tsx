import styles from "../Home/home.module.scss";
import { DragDropContext, Droppable, Draggable, OnDragEndResponder } from "@hello-pangea/dnd";
import { useChatStore } from "../../store";
import Locale from "../../locales";
import { useLocation, useNavigate } from "react-router-dom";
import { Path } from "../../constant";
import { useRef, useEffect } from "react";
import { showConfirm } from "../ui-lib";
import clsx from "clsx";
import { Dropdown, Button, Modal } from "antd";
import { EllipsisOutlined, DeleteOutlined, ExclamationCircleFilled } from "@ant-design/icons";
import { TextView } from "../../components/TextView";
import type { MenuInfo } from "rc-menu/lib/interface";

const { confirm } = Modal;

export function ChatItem(props: {
    onClick?: () => void;
    onDelete?: () => void;
    title: string;
    count: number;
    time: string;
    selected: boolean;
    id: string;
    index: number;
    narrow?: boolean;
}) {
    const draggableRef = useRef<HTMLDivElement | null>(null);
    useEffect(() => {
        if (props.selected && draggableRef.current) {
            draggableRef.current?.scrollIntoView({
                block: "center",
            });
        }
    }, [props.selected]);

    const { pathname: currentPath } = useLocation();

    const confirmDelete = () => {
        confirm({
            title: "确定删除对话?",
            icon: <ExclamationCircleFilled />,
            content: "删除后，聊天记录将不可恢复。",
            okText: "确定",
            cancelText: "取消",
            onOk() {
                return props.onDelete?.();
            },
            onCancel() {},
        });
    };

    const items = [
        {
            key: "delete",
            label: "删除",
            icon: <DeleteOutlined />,
            onClick: (info: MenuInfo) => {
                info.domEvent.stopPropagation(); // 使用 info.domEvent 阻止冒泡
                confirmDelete();
            },
        },
    ];

    return (
        <Draggable draggableId={`${props.id}`} index={props.index}>
            {(provided) => (
                <div
                    className={clsx(styles["chat-item"], {
                        [styles["chat-item-selected"]]:
                            props.selected && (currentPath === Path.Chat || currentPath === Path.Home),
                    })}
                    onClick={props.onClick}
                    ref={(ele) => {
                        draggableRef.current = ele;
                        provided.innerRef(ele);
                    }}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                >
                    {props.narrow ? (
                        <div>{props.count}</div>
                    ) : (
                        <div className={styles["chat-item-content"]}>
                            <div className={styles["chat-item-title"]}>
                                <TextView text={props.title} fontSize={"12px"} placement="right" />
                            </div>
                            <div className={styles["chat-item-btn"]}>
                                <Dropdown
                                    menu={{ items }}
                                    trigger={["click"]}
                                    placement="bottomRight"
                                    getPopupContainer={() => document.body}
                                >
                                    <Button
                                        type="text"
                                        icon={<EllipsisOutlined />}
                                        onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                        }}
                                    />
                                </Dropdown>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </Draggable>
    );
}

export function ChatList(props: { narrow?: boolean }) {
    const [sessions, selectedIndex, selectSession, moveSession] = useChatStore((state) => [
        state.sessions,
        state.currentSessionIndex,
        state.selectSession,
        state.moveSession,
    ]);
    const chatStore = useChatStore();
    const navigate = useNavigate();

    const onDragEnd: OnDragEndResponder = (result) => {
        const { destination, source } = result;
        if (!destination) {
            return;
        }

        if (destination.droppableId === source.droppableId && destination.index === source.index) {
            return;
        }

        moveSession(source.index, destination.index);
    };

    return (
        <DragDropContext onDragEnd={onDragEnd}>
            <div style={{ paddingLeft: "24px" }}>
                <Droppable droppableId="chat-list">
                    {(provided) => (
                        <div className={styles["chat-list"]} ref={provided.innerRef} {...provided.droppableProps}>
                            {sessions.map((item, i) => (
                                <ChatItem
                                    title={item.topic}
                                    time={new Date(item.lastUpdate).toLocaleString()}
                                    count={item.messages.length}
                                    key={item.id}
                                    id={item.id}
                                    index={i}
                                    selected={i === selectedIndex}
                                    onClick={() => {
                                        navigate(Path.Chat);
                                        selectSession(i);
                                    }}
                                    onDelete={async () => {
                                        if (!props.narrow || (await showConfirm(Locale.Home.DeleteChat))) {
                                            chatStore.deleteSession(i);
                                        }
                                    }}
                                    narrow={props.narrow}
                                />
                            ))}
                            {provided.placeholder}
                        </div>
                    )}
                </Droppable>
            </div>
        </DragDropContext>
    );
}
