.interview-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 1);
    padding: 20px 20px 0;
    display: flex;
    flex-direction: column;

    .breadcrumb-wrapper {
        margin-bottom: 0;
    }

    .content-wrapper {
        display: flex;
        flex-direction: column;
        flex: 1;
        background-color: #fff;
        margin-top: 20px;
    }

    :global(.ant-tabs .ant-tabs-tab) {
        padding-top: 0;
    }

    :global(.ant-tabs) {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        overflow: hidden;
    }

    :global(.ant-tabs-content-holder) {
        flex-grow: 1;
        overflow: hidden;
    }

    :global(.ant-tabs-content, .ant-tabs-tabpane) {
        height: 100%;
        width: 100%;
    }
}

.tab-content {
    height: calc(100vh - 140px);
    // overflow: auto;
    overflow: hidden;

    // Responsive adjustments for smaller screens
    @media (max-height: 600px) {
        height: calc(100vh - 120px);
    }

    @media (max-width: 768px) {
        height: calc(100vh - 100px);
    }
}

.interview-tabs {
    :global {
        .ant-tabs-tab-active {
            .ant-tabs-tab-btn {
                color: rgba(0, 153, 242, 1) !important;
                text-shadow: 0 0 0.25px rgba(0, 153, 242, 1) !important;
            }
        }
    }
}
