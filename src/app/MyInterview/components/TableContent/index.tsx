import styles from "./index.module.scss";
import React, { useState, useEffect, useRef, useMemo, useCallback } from "react";
import { Button, Tooltip, Table, Tag, Avatar } from "antd";
import ZoomIcon from "@/app/icons/talentTool/zoom.svg";
import ZoomOutIcon from "@/app/icons/talentTool/zoomout.svg";
import FilterBar, { type FilterOption, type SortOption } from "@/app/components/FilterBar";
import type { TableColumnsType, TableProps } from "antd";
import { INTERVIEW_TYPE, FILTER_TYPE } from "@/app/constant";
import useFullScreenStore from "@/app/store/modules/fullscreen";
import { FilterState, useFilterState } from "@/app/hooks/useFilterState";
import { InterviewerItem, type AssignerInfo, type MetaData } from "@/app/store/modules/interviewer";
import { TextView } from "@/app/components/TextView";
import { InterviewStatus } from "@/app/store/modules/interview-time";
import { Path } from "@/app/constant";
import { useNavigate } from "react-router-dom";
import useCandidateDetailStore from "@/app/store/modules/candidate/detail";

type TableRowSelection<T extends object = object> = TableProps<T>["rowSelection"];

interface DataType {
    key: string;
    title: string;
    jobId: string;
    jobName: string;
    interviewTime: string;
    assigner: AssignerInfo;
    metadata: MetaData;
    candidateId: string;
}

const TableContent = ({ type, allTableData }: { type: string; allTableData: InterviewerItem[] }) => {
    const { isFullScreen, toggleFullScreen, setIsFullScreen } = useFullScreenStore((state) => ({
        isFullScreen: state.isFullScreen,
        toggleFullScreen: state.toggleFullScreen,
        setIsFullScreen: state.setIsFullScreen,
    }));
    const clear = useCandidateDetailStore((state) => state.clear);
    // 筛选和排序
    const { filters, pagination, updateFilter, updateFilters, resetFilters, goToPage, changePageSize, hasFilters } =
        useFilterState({ defaultPageSize: 10 });
    const [filterOptions, setFilterOptions] = useState<FilterOption[]>([]);
    const [sortOptions, setSortOptions] = useState<SortOption[]>([]);
    // 表格
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [tableData, setTableData] = useState<DataType[]>([]);
    const [tableScrollHeight, setTableScrollHeight] = useState<string>("calc(100vh - 430px)");

    const mainContentRef = useRef<HTMLDivElement>(null);
    const tableRef = useRef<HTMLDivElement>(null);

    const navigate = useNavigate();

    const name = useMemo(() => {
        return INTERVIEW_TYPE.find((item) => item.key === type)?.label;
    }, [type]);

    // 计算表格动态高度
    const calculateTableHeight = useCallback(() => {
        if (tableRef.current) {
            const rect = tableRef.current.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const distanceFromTop = rect.top;
            // 预留一些底部空间（如分页器高度等）
            const bottomPadding = 80;
            const calculatedHeight = viewportHeight - distanceFromTop - bottomPadding;
            console.log("calculatedHeight", calculatedHeight);

            setTableScrollHeight(`${Math.max(calculatedHeight, 200)}px`);
        }
    }, []);

    console.log("tableScrollHeight", tableScrollHeight);

    const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
        // 去重
        const uniqueKeys = Array.from(new Set(newSelectedRowKeys));
        setSelectedRowKeys(uniqueKeys);
    };

    const rowSelection: TableRowSelection<DataType> = {
        selectedRowKeys,
        onChange: onSelectChange,
        preserveSelectedRowKeys: true, // 已选择的行在分页时不丢失
        type: "checkbox", // 明确指定为复选框类型
    };

    const columns: TableColumnsType<DataType> = [
        {
            title: "候选人",
            dataIndex: "title",
            width: 80,
            render: (item: string) => {
                const match = item.match(/-([^-]+?)(的面试)?$/);
                return match ? <span>{match[1]}</span> : <span>{item}</span>;
            },
        },
        {
            title: "投递职位",
            dataIndex: "title",
            width: 360,
            // render: (item: string) => {
            //     return <TextView text={item} />;
            // },
        },
        {
            title: "面试信息",
            width: 200,
            dataIndex: "metadata",
            render: (item: MetaData) => {
                return (
                    <div className={styles["interview-info"]}>
                        <div
                            key={item.stageId}
                            style={{ fontSize: "14px", lineHeight: "22px", fontFamily: "PingFang SC" }}
                        >
                            <Tag bordered={false} color="rgba(0, 0, 0, 0.04)">
                                <span style={{ color: "rgba(0, 0, 0, 0.45)" }}>{item.stageName}</span>
                            </Tag>
                            <Tag bordered={false} color="#d9f0fd">
                                <span style={{ color: "rgba(0, 153, 242, 1)" }}>
                                    {item.isVideo ? "视频面试" : "电话面试"}
                                </span>
                            </Tag>
                        </div>
                    </div>
                );
            },
        },
        {
            title: "面试时间",
            dataIndex: "interviewTime",
            width: 400,
            ellipsis: true,
        },
        {
            title: "本轮面试状态",
            dataIndex: "metadata",
            width: 150,
            render: (item: MetaData) => {
                return (
                    <div>
                        {item.interviewState === InterviewStatus.NotStart
                            ? "未开始"
                            : item.interviewState === InterviewStatus.Completed
                              ? "已完成"
                              : "未评价"}
                    </div>
                );
            },
        },
        {
            title: "本轮面试结论",
            dataIndex: "metadata",
            width: 150,
            render: (item: MetaData) => {
                return <div>{item.stateName}</div>;
            },
        },
        {
            title: "面试联系人",
            dataIndex: "assigner",
            width: 240,
            render: (item: AssignerInfo) => {
                return (
                    <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                        <Avatar src={item.avatar} />
                        {item.name}-{item.empId}
                    </div>
                );
            },
        },
        {
            title: "操作",
            fixed: "right",
            width: 160,
            render: (_, item: DataType) => {
                return (
                    <div>
                        <Button
                            type="link"
                            size="small"
                            onClick={() => {
                                clear();
                                navigate(Path.ResumeDetail, {
                                    state: {
                                        jobId: item.jobId,
                                        jobName: item.title,
                                        applicantId: item.candidateId,
                                        mode: "Interviewer",
                                        isManualDistribution: false,
                                    },
                                });
                            }}
                        >
                            查看详情
                        </Button>
                        {/* {item.metadata.interviewState === InterviewStatus.NotResult && (
                            <Button type="link" size="small">
                                去评价
                            </Button>
                        )} */}
                    </div>
                );
            },
        },
    ];

    // 定义筛选条件配置
    const initializeFilterOptions = () => {
        const allFilterOptions: FilterOption[] = [
            {
                key: "education",
                label: "学历",
                type: "select",
                enabled: true,
                width: 120,
                options: [
                    { value: "本科", label: "本科" },
                    { value: "硕士", label: "硕士" },
                    { value: "博士", label: "博士" },
                ],
            },
            {
                key: "workExperience",
                label: "工作年限",
                type: "select",
                enabled: true,
                width: 130,
                options: [
                    { value: "1年以下", label: "1年以下" },
                    { value: "1-3年", label: "1-3年" },
                    { value: "3-5年", label: "3-5年" },
                    { value: "5-10年", label: "5-10年" },
                    { value: "10年以上", label: "10年以上" },
                ],
            },
            {
                key: "company",
                label: "公司",
                type: "select",
                enabled: true,
                width: 120,
                options: [
                    { value: "字节跳动", label: "字节跳动" },
                    { value: "阿里巴巴", label: "阿里巴巴" },
                    { value: "腾讯", label: "腾讯" },
                    { value: "百度", label: "百度" },
                ],
            },
            {
                key: "position",
                label: "任职职位",
                type: "select",
                enabled: true,
                width: 130,
                options: [
                    { value: "前端工程师", label: "前端工程师" },
                    { value: "后端工程师", label: "后端工程师" },
                    { value: "算法工程师", label: "算法工程师" },
                    { value: "产品经理", label: "产品经理" },
                ],
            },
            {
                key: "school",
                label: "学校",
                type: "select",
                enabled: true,
                width: 120,
                options: [
                    { value: "清华大学", label: "清华大学" },
                    { value: "北京大学", label: "北京大学" },
                    { value: "复旦大学", label: "复旦大学" },
                    { value: "上海交通大学", label: "上海交通大学" },
                ],
            },
            {
                key: "skills",
                label: "技能标签",
                type: "multiple",
                enabled: true,
                width: 180,
                options: [
                    { value: "JavaScript", label: "JavaScript" },
                    { value: "React", label: "React" },
                    { value: "Vue", label: "Vue" },
                    { value: "Node.js", label: "Node.js" },
                    { value: "Python", label: "Python" },
                    { value: "Java", label: "Java" },
                ],
            },
            {
                key: "graduationDate",
                label: "毕业时间",
                type: "date",
                enabled: true,
                width: 200,
            },
        ];
        setFilterOptions(allFilterOptions);
    };

    // 初始化排序选项
    const initializeSortOptions = () => {
        const defaultSortOptions: SortOption[] = [{ key: "interviewTime", label: "面试时间", direction: null }];
        setSortOptions(defaultSortOptions);
    };
    const handlePageChange = useCallback(
        (page: number, pageSize: number) => {
            if (pageSize !== pagination.pageSize) {
                changePageSize(pageSize);
            } else {
                goToPage(page - 1);
            }
        },

        [pagination.pageSize, changePageSize, goToPage]
    );
    const handlePageSizeChange = (pageSize: number) => {
        handlePageChange(0, pageSize);
    };

    //实现排序逻辑
    const handleSortChange = (newSortOptions: SortOption[]) => {
        setSortOptions(newSortOptions);
        const activeSort = newSortOptions.find((opt) => opt.direction);
        if (activeSort) {
            const sortedData = [...tableData].sort((a, b) => {
                const aValue = a[activeSort.key as keyof DataType];
                const bValue = b[activeSort.key as keyof DataType];
                if (typeof aValue === "string" && typeof bValue === "string") {
                    return activeSort.direction === "asc" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
                }
                return 0;
            });
            setTableData(sortedData);
        }
    };
    const handleSearch = useCallback(
        (keyword: string) => {
            updateFilter("keyword", keyword);
        },
        [updateFilter]
    );
    const handleMainFilterChange = useCallback(
        (newFilters: Partial<FilterState>) => {
            updateFilters(newFilters);
        },
        [updateFilters]
    );
    const handleFilterOptionsChange = (newFilterOptions: FilterOption[]) => {
        setFilterOptions(newFilterOptions);
    };

    useEffect(() => {
        let filteredData = allTableData;

        // 根据type过滤数据
        if (type !== FILTER_TYPE.All) {
            // "1"是全部，不需要过滤
            filteredData = allTableData.filter((item) => {
                switch (type) {
                    case FILTER_TYPE.NotStarted: // 未开始
                        return item.metadata.interviewState === InterviewStatus.NotStart;
                    case FILTER_TYPE.NotEvaluated: // 未评价
                        return item.metadata.interviewState === InterviewStatus.NotResult;
                    case FILTER_TYPE.Evaluated: // 已完成
                        return item.metadata.interviewState === InterviewStatus.Completed;
                    default:
                        return true;
                }
            });
        }

        const generatedData = filteredData.map<DataType>((item) => {
            return {
                key: item.candidateId,
                title: item.title,
                jobId: item.jobId,
                assigner: item.assigner,
                interviewTime: `${item.gmtStart} - ${item.gmtEnd}`,
                metadata: item.metadata,
                jobName: item.jobName,
                candidateId: item.candidateId,
            };
        });
        setTableData(generatedData);

        // 清理无效的选择状态：当数据更新时，移除不存在的选择项
        const validKeys = generatedData.map((item) => item.key);
        setSelectedRowKeys((prevKeys) => prevKeys.filter((key) => validKeys.includes(key as string)));
    }, [allTableData, type]);

    useEffect(() => {
        initializeFilterOptions();
        initializeSortOptions();
    }, []);

    // 监听全屏状态变化
    useEffect(() => {
        const handleFullscreenChange = () => {
            setIsFullScreen(!!document.fullscreenElement);
        };
        document.addEventListener("fullscreenchange", handleFullscreenChange);

        return () => {
            document.removeEventListener("fullscreenchange", handleFullscreenChange);
        };
    }, []);

    // 计算表格高度并监听窗口大小变化
    useEffect(() => {
        // 初始计算
        const timer = setTimeout(() => {
            calculateTableHeight();
        }, 100); // 延迟一点确保DOM已渲染

        // 监听窗口大小变化
        const handleResize = () => {
            calculateTableHeight();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            clearTimeout(timer);
            window.removeEventListener("resize", handleResize);
        };
    }, [calculateTableHeight]);

    // 监听全屏状态变化时重新计算高度
    useEffect(() => {
        if (tableRef.current) {
            const timer = setTimeout(() => {
                calculateTableHeight();
            }, 100);
            return () => clearTimeout(timer);
        }
    }, [isFullScreen, calculateTableHeight]);

    return (
        <div
            className={`${styles["Table-container"]} ${isFullScreen ? styles["Table-container--fullscreen"] : ""}`}
            ref={mainContentRef}
            id="myInterviewId"
        >
            <div className={styles["Table-header"]}>
                <div className={styles["Table-header-title"]}>{name}面试</div>
                <Tooltip title={isFullScreen ? "退出全屏" : "进入全屏"}>
                    <Button
                        type="text"
                        icon={isFullScreen ? <ZoomOutIcon /> : <ZoomIcon />}
                        className={styles["more-button"]}
                        onClick={() => toggleFullScreen(mainContentRef.current, "myInterviewId")}
                    />
                </Tooltip>
            </div>
            {/* 筛选栏 */}
            <FilterBar
                filters={filters}
                onSearch={handleSearch}
                onFilterChange={handleMainFilterChange}
                onResetFilters={resetFilters}
                hasFilters={hasFilters}
                filterOptions={filterOptions}
                onFilterOptionsChange={handleFilterOptionsChange}
                sortOptions={sortOptions}
            />
            <div className={styles["Table-body"]} ref={tableRef}>
                <Table
                    columns={columns}
                    dataSource={tableData}
                    rowSelection={rowSelection}
                    style={{ marginTop: 16 }}
                    scroll={{ y: `calc(100vh - ${tableScrollHeight})` }}
                    pagination={{
                        size: "small",
                        current: pagination.pageNum + 1,
                        pageSize: pagination.pageSize,
                        total: pagination.total,
                        hideOnSinglePage: false,
                        showSizeChanger: true,
                        pageSizeOptions: [10, 20, 50, 100],
                        onChange: (page, pageSize) => {
                            handlePageChange(page, pageSize);
                        },
                        onShowSizeChange: (current, size) => {
                            handlePageSizeChange(size);
                        },
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                ></Table>
            </div>
        </div>
    );
};

export default TableContent;
