.Table-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    transition: all 0.3s ease;

    &--fullscreen {
        padding: 20px;
    }

    .Table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .Table-header-title {
            color: rgba(0, 0, 0, 0.86);
            font-family: "PingFang SC";
            font-weight: 500;
            font-size: 18px;
            line-height: 25px;
        }
    }
    .Table-body {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        // 命中不到pagination的元素，暂时命中的ul
        ul {
            margin: 16px 0 20px !important;
        }

        :global(.ant-table-wrapper) {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        :global(.ant-table) {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        :global(.ant-table-body) {
            flex: 1;
            height: auto !important;
            overflow-y: auto !important;
        }
    }
}
