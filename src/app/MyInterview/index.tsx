import styles from "./index.module.scss";
import React, { useState, useEffect } from "react";
import { Ta<PERSON>, Button, type TabsProps } from "antd";
import { RollbackOutlined } from "@ant-design/icons";
import { useLocation } from "react-router-dom";
import TableContent from "./components/TableContent";
import CardContent from "./components/cardContent";
import Candidate from "../Candidate";
import { INTERVIEW_TYPE, FILTER_TYPE } from "@/app/constant";
import useInterviewerStore, { InterviewerItem } from "@/app/store/modules/interviewer";
import MyInterviewIcon from "@/app/icons/sidebar/my-interview.svg";
import FilterCandidate from "./components/FilterCandidate";

// import BreadCrumb from "@/app/components/BreadCrumb";
// import { Path } from "@/app/constant";

const MyInterview = () => {
    const location = useLocation();
    const [tableData, setTableData] = useState<InterviewerItem[]>([]);
    const { getInterviewerList } = useInterviewerStore();

    const defaultActiveKey = location.state?.tab || FILTER_TYPE.ResumeScreen;

    useEffect(() => {
        const fetchData = async () => {
            const res = await getInterviewerList({});
            setTableData(res.interviews);
        };
        fetchData();
    }, [getInterviewerList]);

    const tabItems: TabsProps["items"] = INTERVIEW_TYPE.map((item) => {
        return {
            ...item,
            children: (
                <div className={styles["tab-content"]}>
                    {item.key === FILTER_TYPE.ResumeScreen ? (
                        <FilterCandidate key={item.key} />
                    ) : item.key === FILTER_TYPE.ResumeScreened ? (
                        <CardContent mode="Interviewer" />
                    ) : (
                        <TableContent type={item.key} allTableData={tableData} />
                    )}
                </div>
            ),
        };
    });

    return (
        <div className={styles["interview-container"]}>
            {/* 面包屑导航
            <div className={styles["breadcrumb-wrapper"]}>
                <BreadCrumb currentPath={Path.MyInterview} currentTitle="我的面试" />
            </div> */}
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <Button
                    icon={<RollbackOutlined />}
                    onClick={() => {
                        window.history.back();
                    }}
                />
                <MyInterviewIcon />
                我的面试
            </div>
            <div className={styles["content-wrapper"]}>
                <Tabs defaultActiveKey={defaultActiveKey} items={tabItems} className={styles["interview-tabs"]}></Tabs>
            </div>
        </div>
    );
};

export default MyInterview;
