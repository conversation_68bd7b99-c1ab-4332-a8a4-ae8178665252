"use client";

require("../../polyfill");
import { useEffect, useState } from "react";
import styles from "./home.module.scss";
import LogoIcon from "@/app/icons/chat/title-logo.svg";
import LoadingIcon from "../../icons/three-dots.svg";
import { getCSSVar, useMobileScreen } from "../../utils";
import dynamic from "next/dynamic";
import { Path, SlotID } from "../../constant";
import { ErrorBoundary } from "../Error/error";
import { getISOLang } from "../../locales";
import { HashRouter as Router, Route, Routes, useLocation, Navigate } from "react-router-dom";
import { SideBar } from "../LeftSidebar/index";
import { useAppConfig } from "../../store/config";
import { getClientConfig } from "../../config/client";
import { type ClientApi, getClientApi } from "../../client/api";
import { useAccessStore } from "../../store";
import clsx from "clsx";
import RightSidebar from "../RightSidebar";
import useUserInfoStore from "../../store/userInfo";

export function Loading(props: { noLogo?: boolean }) {
    return (
        <div className={clsx("no-dark", styles["loading-content"])}>
            {!props.noLogo && <LogoIcon />}
            <LoadingIcon />
        </div>
    );
}

const Artifacts = dynamic(async () => (await import("../artifacts")).Artifacts, {
    loading: () => <Loading noLogo />,
});
const Settings = dynamic(async () => (await import("../settings")).Settings, {
    loading: () => <Loading noLogo />,
});
const Chat = dynamic(async () => (await import("../Chat")).Chat, {
    loading: () => <Loading noLogo />,
});
const NewChat = dynamic(async () => (await import("../NewChat")).NewChat, {
    loading: () => <Loading noLogo />,
});
const SearchChat = dynamic(async () => (await import("../search-chat")).SearchChatPage, {
    loading: () => <Loading noLogo />,
});
const Dashboard = dynamic(async () => await import("../Dashboard"), {
    loading: () => <Loading noLogo />,
});
const TalentPool = dynamic(async () => await import("../TalentPoolNew"), {
    loading: () => <Loading noLogo />,
});
const FlowHistory = dynamic(async () => await import("../Dashboard/FlowHistory"), {
    loading: () => <Loading noLogo />,
});
const Login = dynamic(async () => await import("../Login"), {
    loading: () => <Loading noLogo />,
});
const Auth = dynamic(async () => await import("../../auth/page"), {
    ssr: false,
    loading: () => <Loading noLogo />,
});
const FlowPublish = dynamic(async () => await import("../Dashboard/FlowPublish"), {
    loading: () => <Loading noLogo />,
});
const JobManage = dynamic(async () => await import("../Dashboard/JobManage"), {
    loading: () => <Loading noLogo />,
});

// 淘汰池
const EliminationPool = dynamic(async () => await import("../EliminationPool"), {
    loading: () => <Loading noLogo />,
});

const ResumeDetail = dynamic(async () => await import("../ResumeDetail"), {
    loading: () => <Loading noLogo />,
});
const Interviewer = dynamic(async () => await import("../Interviewer"), {
    loading: () => <Loading noLogo />,
});
// 面试时间
const InterviewTime = dynamic(async () => await import("../InterviewTime"), {
    loading: () => <Loading noLogo />,
});
// 我的面试
const MyInterview = dynamic(async () => await import("../MyInterview"), {
    loading: () => <Loading noLogo />,
});
const Candidate = dynamic(async () => await import("../Candidate"), {
    loading: () => <Loading noLogo />,
});
const JobDetail = dynamic(async () => await import("../JobDetail"), {
    loading: () => <Loading noLogo />,
});
const JobEdit = dynamic(async () => await import("../JobDetail/JobEdit"), {
    loading: () => <Loading noLogo />,
});

export function useSwitchTheme() {
    const config = useAppConfig();

    useEffect(() => {
        document.body.classList.remove("light");
        document.body.classList.remove("dark");

        if (config.theme === "dark") {
            document.body.classList.add("dark");
        } else if (config.theme === "light") {
            document.body.classList.add("light");
        }

        const metaDescriptionDark = document.querySelector('meta[name="theme-color"][media*="dark"]');
        const metaDescriptionLight = document.querySelector('meta[name="theme-color"][media*="light"]');

        if (config.theme === "auto") {
            metaDescriptionDark?.setAttribute("content", "#151515");
            metaDescriptionLight?.setAttribute("content", "#fafafa");
        } else {
            const themeColor = getCSSVar("--theme-color");
            metaDescriptionDark?.setAttribute("content", themeColor);
            metaDescriptionLight?.setAttribute("content", themeColor);
        }
    }, [config.theme]);
}

function useHtmlLang() {
    useEffect(() => {
        const lang = getISOLang();
        const htmlLang = document.documentElement.lang;

        if (lang !== htmlLang) {
            document.documentElement.lang = lang;
        }
    }, []);
}

const useHasHydrated = () => {
    const [hasHydrated, setHasHydrated] = useState<boolean>(false);

    useEffect(() => {
        setHasHydrated(true);
    }, []);

    return hasHydrated;
};

export function WindowContent(props: { children: React.ReactNode }) {
    return (
        <div className={styles["window-content"]} id={SlotID.AppBody}>
            {props?.children}
        </div>
    );
}

function Screen() {
    const config = useAppConfig();
    const location = useLocation();
    const isArtifact = location.pathname.includes(Path.Artifacts);
    const isHome = location.pathname === Path.Home;
    // const isAuth = location.pathname === Path.Auth;

    const isMobileScreen = useMobileScreen();
    const shouldTightBorder = getClientConfig()?.isApp || (config.tightBorder && !isMobileScreen);

    const { token, logging } = useUserInfoStore((state) => ({ token: state.token, logging: state.logging }));

    if (isArtifact) {
        return (
            <Routes>
                <Route path="/artifacts/:id" element={<Artifacts />} />
            </Routes>
        );
    }

    if (!token) {
        return (
            // 重定向到鉴权页面
            <ErrorBoundary>
                <Routes>
                    <Route path="*" element={<Navigate to={logging ? Path.Auth : Path.Login} replace />} />
                    <Route path={Path.Auth} element={<Auth />} />
                    <Route path={Path.Login} element={<Login />} />
                </Routes>
            </ErrorBoundary>
        );
    }

    const renderContent = () => {
        return (
            <>
                <SideBar
                    className={clsx({
                        [styles["sidebar-show"]]: isHome,
                    })}
                />
                <WindowContent>
                    <Routes>
                        <Route path={Path.Home} element={<NewChat />} />
                        <Route path={Path.NewChat} element={<NewChat />} />
                        <Route path={Path.SearchChat} element={<SearchChat />} />
                        <Route path={Path.Chat} element={<Chat />} />
                        <Route path={Path.Settings} element={<Settings />} />
                        <Route path={Path.Dashboard} element={<Dashboard />} />
                        <Route path={Path.TalentPool} element={<TalentPool />} />
                        <Route path={Path.FlowHistory} element={<FlowHistory />} />
                        <Route path={Path.FlowPublish} element={<FlowPublish />} />
                        <Route path={Path.JobManage} element={<JobManage />} />
                        <Route path={Path.ResumeDetail} element={<ResumeDetail />} />
                        <Route path={Path.Interviewer} element={<Interviewer />} />
                        <Route path={Path.InterviewTime} element={<InterviewTime />} />
                        <Route path={Path.Candidate} element={<Candidate mode="HR" />} />
                        <Route path={Path.MyInterview} element={<MyInterview />} />
                        <Route path={Path.JobDetail} element={<JobDetail />} />
                        <Route path={Path.JobEdit} element={<JobEdit />} />
                        <Route path={Path.EliminationPool} element={<EliminationPool mode="HR" />} />
                    </Routes>
                </WindowContent>
                <RightSidebar></RightSidebar>
            </>
        );
    };

    return (
        <div
            className={clsx(styles.container, {
                [styles["tight-container"]]: shouldTightBorder,
            })}
        >
            {renderContent()}
        </div>
    );
}

export function useLoadData() {
    const config = useAppConfig();

    const api: ClientApi = getClientApi(config.modelConfig.providerName);

    useEffect(() => {
        (async () => {
            const models = await api.llm.models();
            config.mergeModels(models);
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
}

export function Home() {
    useSwitchTheme();
    useLoadData();
    useHtmlLang();

    useEffect(() => {
        console.log("[Config] got config from build time", getClientConfig());
        useAccessStore.getState().fetch();
    }, []);

    if (!useHasHydrated()) {
        return <Loading />;
    }

    return (
        <ErrorBoundary>
            <Router>
                <Screen />
            </Router>
        </ErrorBoundary>
    );
}
