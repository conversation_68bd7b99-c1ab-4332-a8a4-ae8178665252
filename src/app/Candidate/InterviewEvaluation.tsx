import ChatDrawer from "@/app/components/ChatDrawer";
import { forwardRef, memo, useImperativeHandle, useMemo, useRef, useState } from "react";
import useUserInfoStore from "@/app/store/userInfo";
import styles from "./index.module.scss";
import { Avatar, Empty, Flex } from "antd";
import InterviewQuestion from "./InterviewQuestion";
import {
    InterviewDimension,
    InterviewEvaluationExpose,
    InterviewEvaluationTemplateResp,
    InterviewQuestionExpose,
    ResumeResultReq,
} from "@/app/store/modules/candidate";
import {
    candidateNextStageApi,
    getInterviewEvaluationTemplateApi,
    saveBatchResumeResultApi,
    saveResumeResultApi,
} from "@/app/request/modules/candidate";
import { RespParams } from "@/app/typing";
import messageService from "@/app/lib/message";

const InterviewEvaluation = forwardRef<InterviewEvaluationExpose, { refresh: () => void }>((props, ref) => {
    const { refresh } = props;

    const currentUser = useUserInfoStore((state) => state.user);

    const [show, setShow] = useState<boolean>(false);
    const [interviewType, setInterviewType] = useState<number>(1); // 1.不填写评价表，只填写结果与评价；2.填写评价表
    const [jobId, setJobId] = useState<string>("");
    const [templateId, setTemplateId] = useState<string>("");
    const [applicantId, setApplicantId] = useState<string>("");
    const [applicantIds, setApplicantIds] = useState<string[]>([]);
    const [stageId, setStageId] = useState<string>("");
    const [detailList, setDetailList] = useState<InterviewDimension[]>([]);
    const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

    const interviewQuestionRef = useRef<InterviewQuestionExpose>(null);

    useImperativeHandle(ref, () => {
        return {
            showDrawer: async (type, templateId, applicantId, stageId, jobId, initValue) => {
                setShow(true);
                setDetailList([]);
                setInterviewType(type);
                setTemplateId(templateId);
                if (Array.isArray(applicantId)) {
                    setApplicantIds(applicantId);
                } else {
                    setApplicantId(applicantId);
                }
                setStageId(stageId);
                setJobId(jobId);

                if (type === 1) {
                    setDetailList([
                        {
                            dimensionId: "简历初筛结论",
                            weight: 1,
                            isConclusion: true,
                            description: "",
                            questions: [
                                {
                                    questionId: "简历初筛结果",
                                    type: "1",
                                    isRequired: true,
                                    options: [
                                        { value: "通过", score: 1 },
                                        { value: "淘汰", score: 1 },
                                    ],
                                },
                                {
                                    questionId: "简历初筛评价",
                                    type: "3",
                                    isRequired: false,
                                },
                            ],
                        },
                    ]);
                } else {
                    if (templateId) {
                        const res: RespParams<InterviewEvaluationTemplateResp> = await (
                            await getInterviewEvaluationTemplateApi(templateId)
                        ).json();

                        if (res.code === 200) {
                            setDetailList(res.data.detail);
                        }
                    } else {
                        setDetailList([]);
                    }
                }

                const timer = setTimeout(() => {
                    interviewQuestionRef.current?.init(initValue);
                    clearTimeout(timer);
                }, 200);
            },
        };
    });

    const handleSubmit = async () => {
        const validateRes = await interviewQuestionRef.current?.formInstance.validateFields();
        if (validateRes) {
            const formResult = interviewQuestionRef.current?.formInstance.getFieldsValue();
            const formKeyList = Object.keys(formResult);
            const params: ResumeResultReq = {
                templateId: templateId,
                applicantId: applicantId,
                stageId: stageId,
                isPreliminary: interviewType === 1,
                jobId: jobId,
                evaluationContent: {
                    dimensions: detailList.map((item) => {
                        const questionList = item.questions.filter((question) =>
                            formKeyList.includes(question.questionId)
                        );
                        if (questionList) {
                            questionList.forEach((question) => {
                                const currentOption = question.options?.find(
                                    (option) => option.value === formResult[question.questionId]
                                );
                                question.selectedOptions = [
                                    {
                                        score: currentOption?.score ?? 1,
                                        optionDescription: currentOption?.value ?? formResult[question.questionId],
                                    },
                                ];
                            });
                        }
                        return item;
                    }),
                },
                applicantTags: undefined,
                interviewerInfo: {
                    interviewerId: currentUser?.emp_id,
                    name: currentUser?.emp_name,
                    avatarUrl: currentUser?.avatar,
                },
            };
            setConfirmLoading(true);
            if (applicantId) {
                try {
                    const res: RespParams<any> = await (await saveResumeResultApi(params)).json();
                    if (res.code === 200) {
                        messageService.success(res.msg ?? "评价成功");
                        setShow(false);
                        refresh?.();
                    }
                } finally {
                    setConfirmLoading(false);
                }
            } else {
                // 批量保存
                params.applicantId = undefined;
                params.applicantIds = applicantIds;
                try {
                    const res: RespParams<any> = await (await saveBatchResumeResultApi(params)).json();
                    if (res.code === 200) {
                        messageService.success(res.msg ?? "评价成功");
                        setShow(false);
                        refresh?.();
                    }
                } finally {
                    setConfirmLoading(false);
                }
            }
        }
    };

    const renderContent = useMemo(() => {
        return (
            <div className={styles["interview-evaluation-wrapper"]}>
                <Flex align="center" gap={8}>
                    <Avatar src={currentUser.avatar} alt="用户头像" />
                    <span>
                        {currentUser?.emp_id}-{currentUser?.emp_name}
                    </span>
                </Flex>
                <div className={styles["interview-evaluation__content"]}>
                    {detailList.length > 0 ? (
                        <InterviewQuestion ref={interviewQuestionRef} showTags={false} detailList={detailList} />
                    ) : (
                        <Empty />
                    )}
                </div>
            </div>
        );
    }, [currentUser, detailList, interviewQuestionRef]);

    return (
        <ChatDrawer
            title="评价"
            open={show}
            confirmLoading={confirmLoading}
            onCancel={() => setShow(false)}
            onOk={handleSubmit}
        >
            {renderContent}
        </ChatDrawer>
    );
});

InterviewEvaluation.displayName = "InterviewEvaluation";
export default InterviewEvaluation;
