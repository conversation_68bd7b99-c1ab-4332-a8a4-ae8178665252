import { forwardRef, useImperativeHandle, useState, useEffect } from "react";
import ChatDialog from "../../components/ChatDialog";
import { Form, Select, message } from "antd";
import { CandidateAssignExpose } from "@/app/store/modules/candidate";
import { assignCandidateApi } from "@/app/request/modules/candidate";
const Assign = forwardRef<CandidateAssignExpose, { selectedCandidate: any[]; currentJob: any; onSuccess?: () => void }>(
    (props, ref) => {
        const [showDialog, setShowDialog] = useState(false);
        const [confirmLoading, setConfirmLoading] = useState(false);
        const [interviewerList, setInterviewerList] = useState<any[]>([]);

        const [form] = Form.useForm();

        useImperativeHandle(ref, () => {
            return {
                showDialog: () => {
                    setShowDialog(true);
                    form.resetFields();
                },
            };
        });

        const onOk = () => {
            form.validateFields()
                .then((values) => {
                    setConfirmLoading(true);
                    const applicantIds = props.selectedCandidate.map((item) => item.application.applicantId);

                    assignCandidateApi({
                        applicantIds: applicantIds,
                        publishId: props.currentJob?.id,
                        specId: values.scheduleType,
                    })
                        .then(() => {
                            setShowDialog(false);
                            message.success("分配成功");
                            props.onSuccess?.();
                        })
                        .finally(() => {
                            setConfirmLoading(false);
                        });
                })
                .catch();
        };

        useEffect(() => {
            setInterviewerList(props.currentJob?.jobSpecs ?? []);
        }, [props.currentJob]);

        return (
            <ChatDialog
                title="简历分配"
                open={showDialog}
                confirmLoading={confirmLoading}
                onCancel={() => setShowDialog(false)}
                onOk={onOk}
            >
                <div>
                    <Form layout="vertical" form={form}>
                        <Form.Item name="scheduleType" label="岗位" rules={[{ required: true }]}>
                            <Select
                                placeholder="请选择要分配的岗位"
                                options={interviewerList.map((item) => ({
                                    label: `${item.orgName}-${item.jobNameInner}`,
                                    value: item.id,
                                    jobNameInner: item.jobNameInner,
                                    orgName: item.orgName,
                                }))}
                                optionRender={(option) => (
                                    <div>
                                        <div>{option.data.jobNameInner}</div>
                                        <div style={{ fontSize: "12px", color: "rgba(0, 0, 0, 0.45)" }}>
                                            {option.data.orgName}
                                        </div>
                                    </div>
                                )}
                                filterOption={(input, option) =>
                                    (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
                                }
                            />
                        </Form.Item>
                    </Form>
                </div>
            </ChatDialog>
        );
    }
);

Assign.displayName = "Assign";
export default Assign;
