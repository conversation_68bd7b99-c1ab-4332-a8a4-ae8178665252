/**
 * URL 相关的工具函数
 */

/**
 * 获取当前应用的基础URL
 * 根据不同环境自动适配
 * @returns 当前应用的基础URL，如 "http://localhost:3000" 或 "https://hrgenius.inovance.com"
 */
export function getAppBaseUrl(): string {
    if (typeof window !== "undefined") {
        // 客户端环境，直接使用 window.location.origin
        return window.location.origin;
    }

    // 服务端环境，可以根据环境变量或其他方式确定
    // 这里可以根据需要扩展
    return "";
}

/**
 * 根据环境变量获取前端应用的基础URL
 * 这个函数可以用于需要明确指定不同环境URL的场景
 * @returns 环境对应的前端URL
 */
export function getAppBaseUrlByEnv(): string {
    // // 如果在浏览器环境，优先使用当前URL
    if (typeof window !== "undefined") {
        return window.location.origin;
    }

    return "";

    // // 根据 NODE_ENV 或其他环境变量确定URL
    // const nodeEnv = process.env.NODE_ENV;

    // switch (nodeEnv) {
    //     case "development":
    //         return "http://************:9000";
    //     case "test":
    //         return "http://************:9000";
    //     case "production":
    //         return "https://hrgenius.inovance.com";
    //     default:
    //         // 默认返回空字符串
    //         return "";
    // }
}

/**
 * 构建完整的重定向URL
 * @param path 路径，如 "#/interview-time" 或 "/interview-time"
 * @returns 完整的重定向URL
 */
export function buildRedirectUrl(path: string): string {
    const baseUrl = getAppBaseUrl();

    // 确保路径以 / 或 # 开头
    if (!path.startsWith("/") && !path.startsWith("#")) {
        path = "/" + path;
    }

    return `${baseUrl}/${path}`;
}

/**
 * 根据环境获取对应的配置
 * @returns 环境相关的配置信息
 */
export function getEnvironmentConfig() {
    const hostname = typeof window !== "undefined" ? window.location.hostname : "";

    // 根据域名判断环境
    if (hostname.includes("localhost") || hostname.includes("127.0.0.1")) {
        return {
            env: "development",
            name: "开发环境",
        };
    } else if (hostname.includes("************")) {
        return {
            env: "test",
            name: "测试环境",
        };
    } else if (hostname.includes("hrgenius.inovance.com")) {
        return {
            env: "production",
            name: "生产环境",
        };
    } else {
        return {
            env: "unknown",
            name: "未知环境",
        };
    }
}
