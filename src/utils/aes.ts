import CryptoJS from "crypto-js";

// 加密
export const encryptAdvanced = (text: string) => {
    const secretKey = process.env.NEXT_PUBLIC_AES_SECRET_KEY?.toString() ?? "";
    const iv = CryptoJS.enc.Base64.parse(process.env.NEXT_PUBLIC_AES_IV?.toString() ?? "");

    // 加密选项
    const options = {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    };

    // 执行加密
    const encrypted = CryptoJS.AES.encrypt(text, secretKey, options);

    // 将 IV 和密文合并，以便解密时使用
    // 将 IV 转换为 Base64 以便存储
    const ivBase64 = CryptoJS.enc.Base64.stringify(iv);

    // 返回格式: iv:ciphertext
    return ivBase64 + ":" + encrypted.toString();
};

// 高级解密函数
export const decryptAdvanced = (ciphertext: string) => {
    const secretKey = process.env.NEXT_PUBLIC_AES_SECRET_KEY?.toString() ?? "";
    // 分离 IV 和密文
    const textParts = ciphertext.split(":");
    const ivBase64 = textParts[0];
    const encryptedText = textParts[1];

    // 将 Base64 格式的 IV 转回 WordArray
    const iv = CryptoJS.enc.Base64.parse(ivBase64);

    // 解密选项
    const options = {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    };

    // 执行解密
    const decrypted = CryptoJS.AES.decrypt(encryptedText, secretKey, options);

    // 转换为 UTF-8 字符串
    return decrypted.toString(CryptoJS.enc.Utf8);
};
